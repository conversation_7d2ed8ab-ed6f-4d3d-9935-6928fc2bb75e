#!/usr/bin/env python3
"""
Test script to verify the generate button functionality works correctly
"""

import sys
import time
import threading
from unittest.mock import patch, MagicMock

def test_generate_button_functionality():
    """Test that the generate button works without errors."""
    print("🧪 Testing Generate Button Functionality...")
    
    try:
        from AxeIIIPresets import AxeFxPresetGeneratorPro
        
        # Create app instance
        app = AxeFxPresetGeneratorPro()
        
        # Test that required attributes exist
        assert hasattr(app, 'current_blocks'), "Missing current_blocks attribute"
        assert hasattr(app, 'current_preset'), "Missing current_preset attribute"
        assert hasattr(app, 'generate_button'), "Missing generate_button widget"
        assert hasattr(app, 'query_textbox'), "Missing query_textbox widget"
        
        print("✅ All required attributes present")
        
        # Test clear_current_preset method
        app.current_blocks = ["TEST_BLOCK"]
        app.current_preset["blocks"] = ["TEST_BLOCK"]
        
        app.clear_current_preset()
        
        assert app.current_blocks == [], "current_blocks not cleared"
        assert app.current_preset["blocks"] == [], "current_preset blocks not cleared"
        
        print("✅ clear_current_preset method working")
        
        # Test that generate button can be accessed
        button_state = app.generate_button.cget("state")
        print(f"✅ Generate button state: {button_state}")
        
        # Test query textbox
        app.query_textbox.insert("1.0", "Test query")
        query_content = app.query_textbox.get("1.0", "end").strip()
        assert query_content == "Test query", "Query textbox not working"
        app.query_textbox.delete("1.0", "end")
        
        print("✅ Query textbox working")
        
        # Clean up
        app.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Generate button test error: {e}")
        return False

def test_preset_data_structures():
    """Test that preset data structures are properly initialized."""
    print("\n🧪 Testing Preset Data Structures...")
    
    try:
        from AxeIIIPresets import AxeFxPresetGeneratorPro
        
        app = AxeFxPresetGeneratorPro()
        
        # Test current_blocks
        assert isinstance(app.current_blocks, list), "current_blocks should be a list"
        assert app.current_blocks == [], "current_blocks should start empty"
        
        # Test current_preset
        assert isinstance(app.current_preset, dict), "current_preset should be a dict"
        assert "blocks" in app.current_preset, "current_preset should have 'blocks' key"
        assert isinstance(app.current_preset["blocks"], list), "current_preset['blocks'] should be a list"
        
        print("✅ Preset data structures properly initialized")
        
        # Test data manipulation
        test_blocks = ["INPUT", "AMP", "CAB", "OUTPUT"]
        app.current_blocks = test_blocks.copy()
        app.current_preset["blocks"] = test_blocks.copy()
        
        assert app.current_blocks == test_blocks, "current_blocks assignment failed"
        assert app.current_preset["blocks"] == test_blocks, "current_preset blocks assignment failed"
        
        print("✅ Preset data manipulation working")
        
        # Clean up
        app.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Preset data structures test error: {e}")
        return False

def test_ui_components():
    """Test that all UI components are properly created."""
    print("\n🧪 Testing UI Components...")
    
    try:
        from AxeIIIPresets import AxeFxPresetGeneratorPro
        
        app = AxeFxPresetGeneratorPro()
        
        # Test main components
        required_components = [
            'main_container', 'content_container', 'tab_frames',
            'generator_tab', 'templates_tab', 'settings_tab',
            'api_key_entry', 'model_combobox', 'style_combobox',
            'query_textbox', 'generate_button', 'output_text',
            'cpu_progress', 'cpu_label', 'signal_chain_label',
            'status_label', 'nav_buttons'
        ]
        
        missing_components = []
        for component in required_components:
            if not hasattr(app, component):
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ Missing components: {missing_components}")
            return False
        
        print("✅ All required UI components present")
        
        # Test navigation
        app.show_generator_tab()
        app.show_templates_tab()
        app.show_settings_tab()
        app.show_generator_tab()  # Back to generator
        
        print("✅ Tab navigation working")
        
        # Clean up
        app.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI components test error: {e}")
        return False

def main():
    """Run all generate button tests."""
    print("🚀 Starting Generate Button Tests for Modern GUI")
    print("=" * 60)
    
    tests = [
        test_generate_button_functionality,
        test_preset_data_structures,
        test_ui_components
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎉 GENERATE BUTTON TESTS COMPLETED: {passed}/{total} passed")
    
    if passed == total:
        print("✅ Generate button functionality working correctly!")
        print("✅ All preset data structures properly initialized")
        print("✅ Modern GUI components functional")
        print("✅ Ready for preset generation!")
    else:
        print(f"⚠️ {total - passed} tests failed - check implementation")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
