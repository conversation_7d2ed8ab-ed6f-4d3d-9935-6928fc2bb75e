#!/usr/bin/env python3
"""
Test script to verify the optimizations made to AxeIIIPresets.py
"""

import sys
import time
from typing import List, Dict

# Import the optimized classes
try:
    from AxeIIIPresets import (
        <PERSON>rror<PERSON>and<PERSON>, 
        PerformanceOptimizer, 
        CPU_COSTS, 
        FractalGridManager,
        PresetValidator
    )
    print("✅ All optimized classes imported successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_error_handler():
    """Test the centralized error handling."""
    print("\n🧪 Testing ErrorHandler...")
    
    # Test search error handling
    test_error = Exception("Test error message")
    result = ErrorHandler.handle_search_error("test_operation", test_error)
    
    assert result["success"] == False
    assert "test_operation failed" in result["error"]
    assert "results" in result
    print("✅ ErrorHandler.handle_search_error works correctly")
    
    # Test validation error handling
    validation_result = ErrorHandler.handle_validation_error("TEST_BLOCK", test_error)
    assert validation_result["available"] == True
    assert "Validation failed for TEST_BLOCK" in validation_result["error"]
    print("✅ ErrorHandler.handle_validation_error works correctly")

def test_cpu_costs():
    """Test the centralized CPU costs."""
    print("\n🧪 Testing CPU_COSTS...")
    
    # Test that all essential blocks have CPU costs
    essential_blocks = ["IN 1", "OUT 1", "AMP 1", "CAB 1", "DELAY", "REVERB"]
    for block in essential_blocks:
        assert block in CPU_COSTS, f"Missing CPU cost for {block}"
        assert isinstance(CPU_COSTS[block], (int, float)), f"Invalid CPU cost type for {block}"
    
    print(f"✅ CPU_COSTS contains {len(CPU_COSTS)} block definitions")
    print(f"✅ All essential blocks have valid CPU costs")

def test_performance_optimizer():
    """Test the performance optimization utilities."""
    print("\n🧪 Testing PerformanceOptimizer...")
    
    # Create mock grid data
    mock_grid = [
        [{"block": "IN 1"}, {"block": "AMP 1"}, {"block": "OUT 1"}, {"block": None}],
        [{"block": None}, {"block": "DELAY"}, {"block": "."}, {"block": "---"}]
    ]
    
    # Test block extraction
    all_blocks, input_blocks, output_blocks = PerformanceOptimizer.extract_blocks_from_grid(mock_grid)
    
    assert len(all_blocks) == 4  # IN 1, AMP 1, OUT 1, DELAY
    assert len(input_blocks) == 1  # IN 1
    assert len(output_blocks) == 1  # OUT 1
    print("✅ PerformanceOptimizer.extract_blocks_from_grid works correctly")
    
    # Test CPU calculation
    test_blocks = ["IN 1", "AMP 1", "CAB 1", "OUT 1"]
    total_cpu = PerformanceOptimizer.calculate_total_cpu(test_blocks)
    expected_cpu = sum(CPU_COSTS[block] for block in test_blocks)
    
    assert abs(total_cpu - expected_cpu) < 0.01, f"CPU calculation mismatch: {total_cpu} vs {expected_cpu}"
    print("✅ PerformanceOptimizer.calculate_total_cpu works correctly")

def test_fractal_compliance():
    """Test that Axe-Fx III compliance is maintained."""
    print("\n🧪 Testing Fractal Audio Compliance...")
    
    # Test signal flow order
    test_blocks = ["DRIVE", "AMP 1", "CAB 1", "DELAY"]
    sorted_blocks = FractalGridManager.sort_blocks_by_signal_flow(test_blocks)
    
    # Verify order is maintained (DRIVE before AMP before CAB before DELAY)
    drive_pos = next(i for i, block in enumerate(sorted_blocks) if "DRIVE" in block)
    amp_pos = next(i for i, block in enumerate(sorted_blocks) if "AMP" in block)
    cab_pos = next(i for i, block in enumerate(sorted_blocks) if "CAB" in block)
    delay_pos = next(i for i, block in enumerate(sorted_blocks) if "DELAY" in block)
    
    assert drive_pos < amp_pos < cab_pos < delay_pos, "Signal flow order incorrect"
    print("✅ Signal flow order maintained correctly")
    
    # Test basic signal path creation
    basic_path = PresetValidator.create_basic_signal_path()
    assert "IN 1" in basic_path, "Basic path missing INPUT"
    assert "OUT 1" in basic_path, "Basic path missing OUTPUT"
    print("✅ Basic signal path creation works correctly")

def performance_benchmark():
    """Benchmark the optimized functions."""
    print("\n⚡ Performance Benchmark...")
    
    # Benchmark CPU calculation
    test_blocks = ["IN 1", "AMP 1", "CAB 1", "DELAY", "REVERB", "OUT 1"] * 100
    
    start_time = time.time()
    for _ in range(1000):
        total_cpu = PerformanceOptimizer.calculate_total_cpu(test_blocks)
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 1000 * 1000  # Convert to milliseconds
    print(f"✅ CPU calculation: {avg_time:.3f}ms average (1000 iterations)")
    
    # Benchmark grid extraction
    large_grid = [[{"block": f"BLOCK_{i}_{j}"} for j in range(14)] for i in range(6)]
    
    start_time = time.time()
    for _ in range(100):
        all_blocks, input_blocks, output_blocks = PerformanceOptimizer.extract_blocks_from_grid(large_grid)
    end_time = time.time()
    
    avg_time = (end_time - start_time) / 100 * 1000  # Convert to milliseconds
    print(f"✅ Grid extraction: {avg_time:.3f}ms average (100 iterations)")

def main():
    """Run all tests."""
    print("🚀 Starting Optimization Tests for AxeIIIPresets.py")
    print("=" * 60)
    
    try:
        test_error_handler()
        test_cpu_costs()
        test_performance_optimizer()
        test_fractal_compliance()
        performance_benchmark()
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED! Optimizations are working correctly.")
        print("✅ Code quality improved")
        print("✅ Performance optimized")
        print("✅ Axe-Fx III compliance maintained")
        print("✅ Error handling standardized")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
