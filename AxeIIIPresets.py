# Axe-Fx III Preset Generator Professional
# Optimized version with improved performance and code organization

import customtkinter as ctk
import google.generativeai as genai
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import re
import json
from datetime import datetime
import os
import webbrowser
import keyring
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import requests
import time
from bs4 import BeautifulSoup
from typing import Dict, List, Tuple, Optional, Union

# --- Constants and Settings ---
GRID_ROWS: int = 6
GRID_COLS: int = 14

# Security settings for API key storage
APP_NAME: str = "AxeIII_Preset_Generator"
SERVICE_NAME: str = "Gemini_API_Key"
CONFIG_FILE: str = "axe_config.enc"

# Performance optimization constants
MAX_SEARCH_CACHE_SIZE: int = 100
DEFAULT_CACHE_DURATION: int = 3600  # 1 hour
MAX_WEB_SEARCH_RESULTS: int = 10

# Preset validation settings
REQUIRED_INPUT_BLOCKS = ["IN 1", "IN 2", "IN 3", "IN 4", "IN 5"]
REQUIRED_OUTPUT_BLOCKS = ["OUT 1", "OUT 2", "OUT 3", "OUT 4"]
MANDATORY_IO_MESSAGE = """
⚠️ CRITICAL: Every Axe-Fx III preset MUST have INPUT and OUTPUT blocks!

Without these blocks:
• No guitar signal will be processed
• The preset will be silent/non-functional
• Hardware will not receive or output audio

Required components:
• At least one INPUT block (IN 1, IN 2, etc.)
• At least one OUTPUT block (OUT 1, OUT 2, etc.)
"""

# Official Fractal Audio signal flow order (per Axe-Fx III manual)
FRACTAL_SIGNAL_FLOW_ORDER = [
    "INPUT",      # Input blocks (IN 1, IN 2, etc.)
    "DYNAMICS",   # Gate/Expander, Compressor, Limiter
    "DRIVE",      # Drive, Boost, Fuzz, Distortion
    "ROUTING",    # Send/Return blocks for 4-cable method
    "AMP",        # Amp modeling blocks
    "CAB",        # Cabinet simulation
    "EQ",         # Parametric EQ, Graphic EQ, Filter
    "MODULATION", # Chorus, Flanger, Phaser, Tremolo, etc.
    "DELAY",      # All delay types
    "REVERB",     # All reverb types
    "OUTPUT"      # Output blocks (OUT 1, OUT 2, etc.)
]

# Block categorization for proper signal flow
BLOCK_CATEGORIES = {
    "INPUT": ["IN 1", "IN 2", "IN 3", "IN 4", "IN 5"],
    "DYNAMICS": ["GATE/EXP", "COMPRESSOR", "MULTIBAND COMP", "LIMITER", "DYNAMICS"],
    "DRIVE": ["DRIVE", "BOOST", "FUZZ", "DISTORTION"],
    "ROUTING": ["SEND", "RETURN", "SHUNT", "MULTIPLEXER", "FX LOOP"],
    "AMP": ["AMP 1", "AMP 2", "PREAMP"],
    "CAB": ["CAB 1", "CAB 2"],
    "EQ": ["PEQ", "GEQ", "FILTER", "TONE MATCH", "TONE CONTROL"],
    "MODULATION": ["CHORUS", "FLANGER", "PHASER", "ROTARY", "TREMOLO", "RING MOD", "VIBE", "PANNER", "PITCH", "HARMONIZER", "WHAMMY", "CRYSTALS"],
    "DELAY": ["DELAY", "MULTI DELAY", "PLEX DELAY", "MEGATAP DELAY", "DUCKING DELAY", "VINTAGE DELAY", "TAPE DELAY"],
    "REVERB": ["REVERB", "SPRING", "HALL", "PLATE", "ROOM"],
    "OUTPUT": ["OUT 1", "OUT 2", "OUT 3", "OUT 4"],
    "SPECIAL": ["WAH", "FORMANT", "VOCODER", "SYNTH", "RESONATOR", "TALK BOX"],
    "UTILITY": ["VOL/PAN", "MIXER", "LOOPER", "RTA", "CROSSOVER", "FEEDBACK SEND", "FEEDBACK RETURN", "SCENE MIDI", "CONTROLLERS"]
}

# Professional color palette based on actual Axe-Fx III categories
BLOCK_COLORS = {
    # Input/Output - correct blocks according to Axe-Fx III
    "IN 1": "#2E86C1",
    "IN 2": "#2E86C1", 
    "IN 3": "#2E86C1",
    "IN 4": "#2E86C1",
    "IN 5": "#2E86C1",
    "OUT 1": "#28B463",
    "OUT 2": "#28B463",
    "OUT 3": "#28B463",
    "OUT 4": "#28B463",
    "FX LOOP": "#3498DB",
    
    # Dynamics
    "GATE/EXP": "#E74C3C",
    "COMPRESSOR": "#F1C40F",
    "MULTIBAND COMP": "#B9770E",
    "LIMITER": "#E67E22",
    "DYNAMICS": "#F39C12",
    
    # Drive
    "DRIVE": "#C0392B",
    "BOOST": "#CB4335",
    "FUZZ": "#922B21",
    "DISTORTION": "#A93226",
    
    # Amp/Cab
    "AMP 1": "#8B0000",
    "AMP 2": "#8B0000",
    "CAB 1": "#A52A2A",
    "CAB 2": "#A52A2A",
    "PREAMP": "#CD5C5C",
    
    # EQ
    "PEQ": "#E67E22",
    "GEQ": "#5B2C6F",
    "FILTER": "#CB4335",
    "TONE MATCH": "#D35400",
    
    # Modulation
    "CHORUS": "#1E8449",
    "FLANGER": "#C32AFF",
    "PHASER": "#27AE60",
    "ROTARY": "#884EA0",
    "TREMOLO": "#229954",
    "RING MOD": "#884EA0",
    "VIBE": "#16A085",
    "PANNER": "#148F77",
    
    # Delay
    "DELAY": "#1A5276",
    "MULTI DELAY": "#2874A6",
    "PLEX DELAY": "#21618C",
    "MEGATAP DELAY": "#1B4F72",
    "DUCKING DELAY": "#2E86C1",
    "VINTAGE DELAY": "#154360",
    "TAPE DELAY": "#1F618D",
    
    # Reverb
    "REVERB": "#884EA0",
    "SPRING": "#7D3C98",
    "HALL": "#6C3483",
    "PLATE": "#5B2C6F",
    "ROOM": "#8E44AD",
    
    # Pitch
    "PITCH": "#D35400",
    "HARMONIZER": "#DC7633",
    "WHAMMY": "#E59866",
    "CRYSTALS": "#F0B27A",
    
    # Special Effects
    "WAH": "#7D3C98",
    "FORMANT": "#9A7D64",
    "VOCODER": "#17A589",
    "SYNTH": "#27AE60",
    "RESONATOR": "#884EA0",
    "TALK BOX": "#1ABC9C",
    
    # Utilities
    "VOL/PAN": "#5D6D7E",
    "MIXER": "#5D6D7E",
    "SEND": "#566573",
    "RETURN": "#566573",
    "LOOPER": "#17A589",
    "RTA": "#5B2C6F",
    "TONE CONTROL": "#45B39D",
    "CROSSOVER": "#34495E",
    "FEEDBACK SEND": "#7F8C8D",
    "FEEDBACK RETURN": "#95A5A6",
    "SCENE MIDI": "#E8DAEF",
    "CONTROLLERS": "#D5DBDB",
    
    # Routing
    "SHUNT": "#606060",
    "MULTIPLEXER": "#7B7D7D",
    "MIXER": "#909497",
    
    # Default
    "default": "#3B3B3B",
    "empty": "#242424"
}

# Centralized CPU costs for performance optimization
CPU_COSTS: Dict[str, float] = {
    # Amps - highest CPU usage
    "AMP 1": 14.0, "AMP 2": 14.0,
    # Cabs
    "CAB 1": 8.0, "CAB 2": 8.0,
    # Reverbs - high CPU
    "REVERB": 7.0, "SPRING": 6.5, "HALL": 7.5, "PLATE": 6.8, "ROOM": 5.5,
    # Delays - medium CPU
    "DELAY": 4.0, "MULTI DELAY": 6.0, "PLEX DELAY": 5.5, "MEGATAP DELAY": 8.0,
    "DUCKING DELAY": 4.5, "VINTAGE DELAY": 3.5, "TAPE DELAY": 5.0,
    # Pitch - high CPU
    "PITCH": 6.0, "HARMONIZER": 8.0, "WHAMMY": 5.0, "CRYSTALS": 7.0,
    # Modulation - medium CPU
    "CHORUS": 3.0, "FLANGER": 3.5, "PHASER": 3.0, "ROTARY": 5.0,
    "TREMOLO": 2.0, "RING MOD": 2.5, "VIBE": 3.5, "PANNER": 1.5,
    # Dynamics - low CPU
    "COMPRESSOR": 2.0, "MULTIBAND COMP": 4.5, "GATE/EXP": 1.5, "LIMITER": 2.0, "DYNAMICS": 2.5,
    # Drive - low CPU
    "DRIVE": 2.5, "BOOST": 1.5, "FUZZ": 2.0, "DISTORTION": 2.5,
    # EQ - low CPU
    "PEQ": 1.0, "GEQ": 2.0, "FILTER": 1.5, "TONE MATCH": 3.5,
    # Special effects
    "WAH": 2.0, "FORMANT": 4.0, "VOCODER": 6.0, "SYNTH": 5.0, "RESONATOR": 4.5, "TALK BOX": 3.5,
    # Utilities - very low CPU
    "VOL/PAN": 0.5, "MIXER": 1.0, "SEND": 0.3, "RETURN": 0.3, "LOOPER": 3.0,
    "RTA": 2.0, "TONE CONTROL": 1.0, "CROSSOVER": 2.5, "FEEDBACK SEND": 0.5,
    "FEEDBACK RETURN": 0.5, "SCENE MIDI": 0.1, "CONTROLLERS": 0.1,
    # Routing - no CPU
    "SHUNT": 0.0, "MULTIPLEXER": 0.5,
    # I/O - minimal CPU
    "IN 1": 0.1, "IN 2": 0.1, "IN 3": 0.1, "IN 4": 0.1, "IN 5": 0.1,
    "OUT 1": 0.1, "OUT 2": 0.1, "OUT 3": 0.1, "OUT 4": 0.1, "FX LOOP": 0.5
}

class ErrorHandler:
    """Centralized error handling for consistent error management."""

    @staticmethod
    def handle_search_error(operation: str, error: Exception) -> Dict[str, Union[bool, str, List]]:
        """Standardized error handling for search operations."""
        return {
            "success": False,
            "error": f"{operation} failed: {str(error)}",
            "results": [],
            "operation": operation
        }

    @staticmethod
    def handle_validation_error(block_name: str, error: Exception) -> Dict[str, Union[bool, str]]:
        """Standardized error handling for validation operations."""
        return {
            "available": True,  # Default assumption for safety
            "error": f"Validation failed for {block_name}: {str(error)}",
            "sources": []
        }

class PerformanceOptimizer:
    """Performance optimization utilities for the application."""

    @staticmethod
    def extract_blocks_from_grid(grid) -> Tuple[List[Tuple[int, int, str]], List[Tuple[int, int, str]], List[Tuple[int, int, str]]]:
        """Optimized single-pass block extraction with categorization."""
        all_blocks: List[Tuple[int, int, str]] = []
        input_blocks: List[Tuple[int, int, str]] = []
        output_blocks: List[Tuple[int, int, str]] = []

        for row_idx, row in enumerate(grid):
            for col_idx, cell in enumerate(row):
                block_name = cell.get("block") if isinstance(cell, dict) else cell
                if block_name and block_name not in ['.', '---', '|', 'X', None]:
                    block_tuple = (row_idx, col_idx, block_name)
                    all_blocks.append(block_tuple)

                    # Categorize during extraction for efficiency
                    category = FractalGridManager.get_block_category(block_name)
                    if category == "INPUT":
                        input_blocks.append(block_tuple)
                    elif category == "OUTPUT":
                        output_blocks.append(block_tuple)

        return all_blocks, input_blocks, output_blocks

    @staticmethod
    def calculate_total_cpu(blocks: List[str]) -> float:
        """Optimized CPU calculation using vectorized operations."""
        return sum(CPU_COSTS.get(block, 1.0) for block in blocks if block not in ['.', '---', '|', 'X', None])

# Amp models categorized by style
AMP_MODELS = {
    "Clean": [
        "Jazz 120 (Roland JC-120)",
        "Twin Verb (Fender Twin)",
        "Deluxe Verb (Fender Deluxe)",
        "AC-20 DLX (Vox AC30)"
    ],
    "Blues/Rock": [
        "59 Bassguy (Tweed Bassman)",
        "Plexi 50W/100W (Marshall)",
        "JCM800 (Marshall)",
        "Dirty Shirley (Friedman)"
    ],
    "High Gain": [
        "PVH 6160 (Peavey)",
        "Recto1/2 (Mesa Dual Rec)",
        "Mark IIC+ (Mesa Boogie)",
        "5153 Blue/Red (EVH)"
    ],
    "Modern": [
        "FAS Modern II/III",
        "Friedman BE/HBE",
        "ENGL Savage",
        "Diezel VH4"
    ]
}

# Official Fractal Audio signal flow chains - following proper order per Axe-Fx III manual
EFFECT_CHAINS = {
    "Metal": ["IN 1", "GATE/EXP", "DRIVE", "AMP 1", "CAB 1", "GEQ", "DELAY", "REVERB", "OUT 1"],
    "Rock": ["IN 1", "COMPRESSOR", "DRIVE", "AMP 1", "CAB 1", "CHORUS", "DELAY", "REVERB", "OUT 1"],
    "Blues": ["IN 1", "COMPRESSOR", "DRIVE", "AMP 1", "CAB 1", "TREMOLO", "DELAY", "SPRING", "OUT 1"],
    "Clean": ["IN 1", "COMPRESSOR", "AMP 1", "CAB 1", "CHORUS", "DELAY", "REVERB", "OUT 1"],
    "Ambient": ["IN 1", "COMPRESSOR", "AMP 1", "CAB 1", "CHORUS", "MULTI DELAY", "HALL", "OUT 1"],
    "Fusion": ["IN 1", "COMPRESSOR", "AMP 1", "CAB 1", "CHORUS", "DELAY", "REVERB", "OUT 1"],
    "Djent": ["IN 1", "GATE/EXP", "DRIVE", "AMP 1", "CAB 1", "GEQ", "DELAY", "OUT 1"],
    "4CM": ["IN 1", "COMPRESSOR", "DRIVE", "SEND", "RETURN", "CAB 1", "DELAY", "REVERB", "OUT 1"]
}

# Known presets by artists
ARTIST_PRESETS = {
    "James Hetfield": {
        "amps": ["Recto2 Red Modern", "Mark IIC+"],
        "drive": "T808 MOD",
        "eq": "V-shape (80Hz +3, 750Hz -5, 2.2kHz +4)",
        "gate": "-45dB threshold"
    },
    "John Petrucci": {
        "amps": ["Mark IIC+", "JP2C"],
        "drive": "T808",
        "delay": "7ms + 1/4 note",
        "eq": "V-shape graphic"
    },
    "David Gilmour": {
        "amps": ["Hipower Normal"],
        "effects": ["Electric Mistress", "Binson Echorec"],
        "reverb": "Large Hall"
    },
    "Eddie Van Halen": {
        "amps": ["Plexi 100W High"],
        "effects": ["MXR Phase 90", "MXR Flanger"],
        "special": "Variac sim (90V)"
    }
}

class SecureAPIKeyManager:
    """
    Manages secure storage and retrieval of API keys using multiple methods:
    1. OS keyring (primary) - Windows Credential Manager, macOS Keychain, Linux Secret Service
    2. Encrypted local file (fallback)
    """

    def __init__(self):
        self.app_name = APP_NAME
        self.service_name = SERVICE_NAME
        self.config_file = CONFIG_FILE

    def save_api_key(self, api_key, use_keyring=True):
        """
        Saves API key securely using the best available method.

        Args:
            api_key (str): The API key to save
            use_keyring (bool): Whether to try keyring first

        Returns:
            tuple: (success: bool, method: str, error: str)
        """
        if not api_key or not api_key.strip():
            return False, "", "API key cannot be empty"

        # Try keyring first (most secure)
        if use_keyring:
            try:
                keyring.set_password(self.service_name, self.app_name, api_key)
                return True, "keyring", ""
            except Exception as e:
                # Keyring failed, try encrypted file
                pass

        # Fallback to encrypted file
        try:
            self._save_to_encrypted_file(api_key)
            return True, "encrypted_file", ""
        except Exception as e:
            return False, "", f"Failed to save API key: {str(e)}"

    def load_api_key(self):
        """
        Loads API key from secure storage.

        Returns:
            tuple: (api_key: str, method: str, error: str)
        """
        # Try keyring first
        try:
            api_key = keyring.get_password(self.service_name, self.app_name)
            if api_key:
                return api_key, "keyring", ""
        except Exception as e:
            pass

        # Try encrypted file
        try:
            api_key = self._load_from_encrypted_file()
            if api_key:
                return api_key, "encrypted_file", ""
        except Exception as e:
            pass

        return "", "", "No saved API key found"

    def clear_api_key(self):
        """
        Clears saved API key from all storage methods.

        Returns:
            tuple: (success: bool, methods_cleared: list, errors: list)
        """
        methods_cleared = []
        errors = []

        # Clear from keyring
        try:
            keyring.delete_password(self.service_name, self.app_name)
            methods_cleared.append("keyring")
        except Exception as e:
            if "not found" not in str(e).lower():
                errors.append(f"Keyring: {str(e)}")

        # Clear encrypted file
        try:
            if os.path.exists(self.config_file):
                os.remove(self.config_file)
                methods_cleared.append("encrypted_file")
        except Exception as e:
            errors.append(f"File: {str(e)}")

        success = len(methods_cleared) > 0 or len(errors) == 0
        return success, methods_cleared, errors

    def _generate_key_from_machine(self):
        """
        Generates a key based on machine-specific information.
        This provides basic protection but is not as secure as a user password.
        """
        # Use machine-specific info to generate a consistent key
        machine_info = f"{os.environ.get('COMPUTERNAME', '')}{os.environ.get('USERNAME', '')}{os.path.dirname(os.path.abspath(__file__))}"

        # Create a key derivation function
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'axe_iii_preset_salt',  # Fixed salt for consistency
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(machine_info.encode()))
        return key

    def _save_to_encrypted_file(self, api_key):
        """Saves API key to encrypted file."""
        key = self._generate_key_from_machine()
        fernet = Fernet(key)

        encrypted_data = fernet.encrypt(api_key.encode())

        with open(self.config_file, 'wb') as f:
            f.write(encrypted_data)

    def _load_from_encrypted_file(self):
        """Loads API key from encrypted file."""
        if not os.path.exists(self.config_file):
            return None

        key = self._generate_key_from_machine()
        fernet = Fernet(key)

        with open(self.config_file, 'rb') as f:
            encrypted_data = f.read()

        decrypted_data = fernet.decrypt(encrypted_data)
        return decrypted_data.decode()

    def is_api_key_saved(self):
        """
        Checks if an API key is saved in any storage method.

        Returns:
            tuple: (is_saved: bool, methods: list)
        """
        methods = []

        # Check keyring
        try:
            if keyring.get_password(self.service_name, self.app_name):
                methods.append("keyring")
        except:
            pass

        # Check encrypted file
        try:
            if os.path.exists(self.config_file) and self._load_from_encrypted_file():
                methods.append("encrypted_file")
        except:
            pass

        return len(methods) > 0, methods

class WebSearchManager:
    """
    Manages real-time web search integration for enhanced AI preset generation.
    Provides access to current information about Fractal Audio, guitar tones, and music trends.
    """

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.search_cache: Dict[str, Dict] = {}
        self.cache_duration: int = DEFAULT_CACHE_DURATION
        self._cache_access_order: List[str] = []  # For LRU cache management

        # Trusted sources for Fractal Audio information
        self.trusted_sources = [
            'fractal-audio.com',
            'wiki.fractalaudio.com',
            'forum.fractalaudio.com',
            'axechange.fractalaudio.com',
            'youtube.com/c/FractalAudio',
            'reverb.com',
            'sweetwater.com',
            'guitarcenter.com'
        ]

        # Search endpoints
        self.search_engines = {
            'google': 'https://www.googleapis.com/customsearch/v1',
            'bing': 'https://api.bing.microsoft.com/v7.0/search',
            'duckduckgo': 'https://api.duckduckgo.com/'
        }

    def search_fractal_audio_info(self, query, search_type="general"):
        """
        Searches for Fractal Audio specific information.

        Args:
            query (str): Search query
            search_type (str): Type of search (firmware, blocks, presets, artists)

        Returns:
            dict: Search results with source validation
        """
        # Enhance query with Fractal Audio context
        enhanced_queries = {
            "firmware": f"Fractal Audio Axe-Fx III firmware {query} site:fractal-audio.com OR site:wiki.fractalaudio.com",
            "blocks": f"Axe-Fx III {query} block effects site:fractal-audio.com OR site:wiki.fractalaudio.com",
            "presets": f"Axe-Fx III preset {query} site:axechange.fractalaudio.com OR site:forum.fractalaudio.com",
            "artists": f"guitarist {query} Axe-Fx III tone settings equipment",
            "general": f"Fractal Audio Axe-Fx III {query}"
        }

        search_query = enhanced_queries.get(search_type, enhanced_queries["general"])

        try:
            # Check cache first
            cache_key = f"{search_type}_{query}"
            if self._is_cached(cache_key):
                return self.search_cache[cache_key]

            # Perform web search
            results = self._perform_web_search(search_query)

            # Validate and rank results
            validated_results = self._validate_sources(results)

            # Cache results
            self._cache_results(cache_key, validated_results)

            return validated_results

        except Exception as e:
            return ErrorHandler.handle_search_error("fractal_audio_search", e)

    def search_tone_references(self, artist=None, song=None, genre=None, equipment=None):
        """
        Searches for tone reference information for specific artists, songs, or genres.

        Returns:
            dict: Tone reference information with source citations
        """
        search_terms = []

        if artist:
            search_terms.append(f'"{artist}" guitar tone')
        if song:
            search_terms.append(f'"{song}" guitar sound')
        if genre:
            search_terms.append(f'{genre} guitar tone settings')
        if equipment:
            search_terms.append(f'{equipment} settings')

        # Add Axe-Fx specific terms
        search_terms.append("Axe-Fx III")
        search_terms.append("Fractal Audio")

        query = " ".join(search_terms)

        try:
            results = self._perform_web_search(query)

            # Filter for relevant tone information
            tone_results = self._filter_tone_content(results)

            return {
                "success": True,
                "tone_info": tone_results,
                "search_query": query,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return ErrorHandler.handle_search_error("tone_references_search", e)

    def get_current_firmware_info(self):
        """
        Retrieves current Fractal Audio firmware information.

        Returns:
            dict: Current firmware version and new features
        """
        try:
            # Search for latest firmware information
            firmware_query = "Fractal Audio Axe-Fx III firmware latest version release notes"
            results = self.search_fractal_audio_info(firmware_query, "firmware")

            if results.get("success"):
                firmware_info = self._extract_firmware_details(results["results"])
                return {
                    "success": True,
                    "firmware_info": firmware_info,
                    "last_updated": datetime.now().isoformat()
                }
            else:
                return {"success": False, "error": "Could not retrieve firmware information"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def search_block_compatibility(self, blocks):
        """
        Searches for block compatibility and current availability.

        Args:
            blocks (list): List of block names to check

        Returns:
            dict: Compatibility information for each block
        """
        compatibility_info = {}

        for block in blocks:
            try:
                query = f"Axe-Fx III {block} block compatibility firmware"
                results = self.search_fractal_audio_info(query, "blocks")

                compatibility_info[block] = {
                    "available": True,  # Default assumption
                    "firmware_required": "Unknown",
                    "notes": "",
                    "sources": []
                }

                if results.get("success") and results.get("results"):
                    # Extract compatibility information
                    block_info = self._extract_block_info(results["results"], block)
                    compatibility_info[block].update(block_info)

            except Exception as e:
                compatibility_info[block] = ErrorHandler.handle_validation_error(block, e)

        return compatibility_info

    def _perform_web_search(self, query):
        """Performs actual web search using available search engines."""
        try:
            # Use DuckDuckGo for privacy-friendly search
            search_url = "https://html.duckduckgo.com/html/"
            params = {
                'q': query,
                'kl': 'us-en'
            }

            response = self.session.get(search_url, params=params, timeout=10)
            response.raise_for_status()

            # Parse search results
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []

            # Extract search result links and snippets
            for result in soup.find_all('div', class_='result'):
                title_elem = result.find('a', class_='result__a')
                snippet_elem = result.find('div', class_='result__snippet')

                if title_elem and snippet_elem:
                    results.append({
                        'title': title_elem.get_text(strip=True),
                        'url': title_elem.get('href', ''),
                        'snippet': snippet_elem.get_text(strip=True)
                    })

            return {
                "success": True,
                "results": results[:10],  # Limit to top 10 results
                "query": query
            }

        except Exception as e:
            # Fallback to basic search simulation
            return {
                "success": False,
                "error": str(e),
                "results": [],
                "fallback": True
            }

    def _validate_sources(self, search_results):
        """Validates and ranks search results based on source trustworthiness."""
        if not search_results.get("success"):
            return search_results

        validated_results = []

        for result in search_results.get("results", []):
            url = result.get("url", "")
            trust_score = 0

            # Check against trusted sources
            for trusted_domain in self.trusted_sources:
                if trusted_domain in url.lower():
                    trust_score += 10
                    break

            # Additional scoring based on content relevance
            title = result.get("title", "").lower()
            snippet = result.get("snippet", "").lower()

            fractal_terms = ["fractal", "axe-fx", "axe fx", "fractal audio"]
            for term in fractal_terms:
                if term in title:
                    trust_score += 3
                if term in snippet:
                    trust_score += 2

            result["trust_score"] = trust_score
            validated_results.append(result)

        # Sort by trust score
        validated_results.sort(key=lambda x: x.get("trust_score", 0), reverse=True)

        return {
            "success": True,
            "results": validated_results,
            "validation_applied": True,
            "trusted_sources_found": len([r for r in validated_results if r.get("trust_score", 0) >= 10])
        }

    def _filter_tone_content(self, search_results):
        """Filters search results for tone-relevant content."""
        if not search_results.get("results"):
            return []

        tone_keywords = [
            "tone", "sound", "settings", "preset", "amp", "cabinet", "effects",
            "eq", "gain", "distortion", "clean", "overdrive", "reverb", "delay"
        ]

        filtered_results = []

        for result in search_results["results"]:
            snippet = result.get("snippet", "").lower()
            title = result.get("title", "").lower()

            relevance_score = 0
            for keyword in tone_keywords:
                if keyword in snippet:
                    relevance_score += 2
                if keyword in title:
                    relevance_score += 3

            if relevance_score >= 4:  # Minimum relevance threshold
                result["tone_relevance"] = relevance_score
                filtered_results.append(result)

        return sorted(filtered_results, key=lambda x: x.get("tone_relevance", 0), reverse=True)

    def _extract_firmware_details(self, search_results):
        """Extracts firmware version and feature information from search results."""
        firmware_info = {
            "version": "Unknown",
            "release_date": "Unknown",
            "new_features": [],
            "new_blocks": [],
            "sources": []
        }

        version_patterns = [
            r'firmware\s+(\d+\.\d+(?:\.\d+)?)',
            r'version\s+(\d+\.\d+(?:\.\d+)?)',
            r'v(\d+\.\d+(?:\.\d+)?)'
        ]

        for result in search_results:
            text = f"{result.get('title', '')} {result.get('snippet', '')}".lower()

            # Extract version numbers
            for pattern in version_patterns:
                matches = re.findall(pattern, text)
                if matches:
                    firmware_info["version"] = matches[0]
                    break

            # Look for new features/blocks
            if "new" in text and any(term in text for term in ["block", "effect", "feature"]):
                firmware_info["sources"].append(result.get("url", ""))

        return firmware_info

    def _extract_block_info(self, search_results, block_name):
        """Extracts block-specific information from search results."""
        block_info = {
            "available": True,
            "firmware_required": "Unknown",
            "notes": "",
            "sources": []
        }

        for result in search_results:
            text = f"{result.get('title', '')} {result.get('snippet', '')}".lower()

            if block_name.lower() in text:
                block_info["sources"].append(result.get("url", ""))

                # Look for firmware requirements
                firmware_match = re.search(r'firmware\s+(\d+\.\d+)', text)
                if firmware_match:
                    block_info["firmware_required"] = firmware_match.group(1)

        return block_info

    def _is_cached(self, cache_key):
        """Checks if search results are cached and still valid."""
        if cache_key not in self.search_cache:
            return False

        cached_time = self.search_cache[cache_key].get("cached_at", 0)
        return (time.time() - cached_time) < self.cache_duration

    def _cache_results(self, cache_key: str, results: Dict) -> None:
        """Caches search results with timestamp and LRU management."""
        # Implement LRU cache with size limit
        if len(self.search_cache) >= MAX_SEARCH_CACHE_SIZE:
            # Remove oldest entry
            if self._cache_access_order:
                oldest_key = self._cache_access_order.pop(0)
                self.search_cache.pop(oldest_key, None)

        results["cached_at"] = time.time()
        self.search_cache[cache_key] = results

        # Update access order
        if cache_key in self._cache_access_order:
            self._cache_access_order.remove(cache_key)
        self._cache_access_order.append(cache_key)

class FractalGridManager:
    """
    Manages grid layout according to official Fractal Audio procedures and best practices.
    Ensures proper signal flow, logical block ordering, and functional preset construction.
    """

    @staticmethod
    def get_block_category(block_name):
        """Returns the category of a block for proper signal flow ordering."""
        for category, blocks in BLOCK_CATEGORIES.items():
            if block_name in blocks:
                return category
        return "SPECIAL"  # Default for unrecognized blocks

    @staticmethod
    def get_signal_flow_position(block_name):
        """Returns the position in signal flow (lower number = earlier in chain)."""
        category = FractalGridManager.get_block_category(block_name)
        try:
            return FRACTAL_SIGNAL_FLOW_ORDER.index(category)
        except ValueError:
            return 999  # Put unrecognized blocks at the end

    @staticmethod
    def sort_blocks_by_signal_flow(blocks):
        """Sorts blocks according to proper Fractal Audio signal flow."""
        return sorted(blocks, key=lambda block: FractalGridManager.get_signal_flow_position(block))

    @staticmethod
    def create_optimal_grid_layout(blocks):
        """
        Creates an optimal grid layout following Fractal Audio best practices.

        Args:
            blocks: List of block names to arrange

        Returns:
            tuple: (grid_layout, connections, warnings)
        """
        # Initialize empty grid
        grid = [[None for _ in range(GRID_COLS)] for _ in range(GRID_ROWS)]
        connections = []
        warnings = []

        # Ensure I/O blocks are present
        if not any(FractalGridManager.get_block_category(block) == "INPUT" for block in blocks):
            blocks = ["IN 1"] + blocks
            warnings.append("Added missing INPUT block (IN 1)")

        if not any(FractalGridManager.get_block_category(block) == "OUTPUT" for block in blocks):
            blocks = blocks + ["OUT 1"]
            warnings.append("Added missing OUTPUT block (OUT 1)")

        # Sort blocks by proper signal flow
        sorted_blocks = FractalGridManager.sort_blocks_by_signal_flow(blocks)

        # Place blocks in grid following left-to-right signal flow
        row = 0
        col = 0

        for i, block in enumerate(sorted_blocks):
            if col >= GRID_COLS:
                # Move to next row if current row is full
                row += 1
                col = 0

            if row >= GRID_ROWS:
                warnings.append(f"Grid full - couldn't place block: {block}")
                break

            # Place block
            grid[row][col] = block

            # Add horizontal connection to next block (if not the last block)
            if i < len(sorted_blocks) - 1 and col < GRID_COLS - 1:
                connections.append({
                    "from": (row, col),
                    "to": (row, col + 1),
                    "type": "horizontal"
                })

            col += 1

        return grid, connections, warnings

    @staticmethod
    def validate_signal_flow(grid) -> Tuple[bool, List[str], List[str], List[str]]:
        """
        Optimized validation that follows proper Fractal Audio signal flow principles.

        Returns:
            tuple: (is_valid, errors, warnings, suggestions)
        """
        errors: List[str] = []
        warnings: List[str] = []
        suggestions: List[str] = []

        # Optimized block extraction with single pass
        blocks: List[Tuple[int, int, str]] = []
        input_blocks: List[Tuple[int, int, str]] = []
        output_blocks: List[Tuple[int, int, str]] = []

        for row_idx, row in enumerate(grid):
            for col_idx, cell in enumerate(row):
                block_name = cell.get("block") if isinstance(cell, dict) else cell
                if block_name and block_name not in ['.', '---', '|', 'X', None]:
                    block_tuple = (row_idx, col_idx, block_name)
                    blocks.append(block_tuple)

                    # Categorize during extraction for efficiency
                    if FractalGridManager.get_block_category(block_name) == "INPUT":
                        input_blocks.append(block_tuple)
                    elif FractalGridManager.get_block_category(block_name) == "OUTPUT":
                        output_blocks.append(block_tuple)

        if not blocks:
            errors.append("❌ Empty grid - no blocks found")
            return False, errors, warnings, suggestions

        # Check for INPUT blocks
        input_blocks = [b for b in blocks if FractalGridManager.get_block_category(b[2]) == "INPUT"]
        if not input_blocks:
            errors.append("❌ CRITICAL: No INPUT block found - preset will not receive signal")
            suggestions.append("💡 Add an INPUT block (IN 1, IN 2, etc.) at the beginning")

        # Check for OUTPUT blocks
        output_blocks = [b for b in blocks if FractalGridManager.get_block_category(b[2]) == "OUTPUT"]
        if not output_blocks:
            errors.append("❌ CRITICAL: No OUTPUT block found - preset will not output signal")
            suggestions.append("💡 Add an OUTPUT block (OUT 1, OUT 2, etc.) at the end")

        # Check signal flow order
        if input_blocks and output_blocks:
            # Find leftmost input and rightmost output
            leftmost_input = min(input_blocks, key=lambda x: (x[0], x[1]))
            rightmost_output = max(output_blocks, key=lambda x: (x[0], x[1]))

            # Check if input comes before output
            if (leftmost_input[0] > rightmost_output[0] or
                (leftmost_input[0] == rightmost_output[0] and leftmost_input[1] >= rightmost_output[1])):
                warnings.append("⚠️ Signal flow: INPUT should come before OUTPUT in the signal chain")

        # Check for proper block ordering within signal flow
        block_positions = []
        for row_idx, col_idx, block in blocks:
            flow_pos = FractalGridManager.get_signal_flow_position(block)
            grid_pos = row_idx * GRID_COLS + col_idx
            block_positions.append((flow_pos, grid_pos, block))

        # Sort by grid position and check if signal flow order is maintained
        block_positions.sort(key=lambda x: x[1])  # Sort by grid position

        for i in range(len(block_positions) - 1):
            current_flow_pos = block_positions[i][0]
            next_flow_pos = block_positions[i + 1][0]

            if current_flow_pos > next_flow_pos and current_flow_pos != 999 and next_flow_pos != 999:
                current_block = block_positions[i][2]
                next_block = block_positions[i + 1][2]
                warnings.append(f"⚠️ Signal flow order: {current_block} should typically come after {next_block}")

        # Check for essential blocks
        amp_blocks = [b for b in blocks if FractalGridManager.get_block_category(b[2]) == "AMP"]
        cab_blocks = [b for b in blocks if FractalGridManager.get_block_category(b[2]) == "CAB"]

        if amp_blocks and not cab_blocks:
            warnings.append("⚠️ AMP block without CAB block - may sound harsh without cabinet simulation")
            suggestions.append("💡 Add a CAB block after the AMP for realistic guitar tone")

        # Check for multiple amps (advanced routing)
        if len(amp_blocks) > 1:
            suggestions.append("💡 Multiple AMP blocks detected - consider using MIXER blocks for proper routing")

        # Overall validation
        is_valid = len(errors) == 0

        return is_valid, errors, warnings, suggestions

class PresetValidator:
    """
    Validates Axe-Fx III presets to ensure they have proper signal flow and required components.
    Every preset MUST have INPUT and OUTPUT blocks to function on hardware.
    """

    @staticmethod
    def validate_preset(grid_cells):
        """
        Validates that a preset has the essential components for proper signal flow.

        Args:
            grid_cells: The grid containing the preset blocks

        Returns:
            tuple: (is_valid: bool, errors: list, warnings: list, suggestions: list)
        """
        errors = []
        warnings = []
        suggestions = []

        # Extract all blocks from grid
        blocks = []
        input_blocks = []
        output_blocks = []
        effect_blocks = []

        for row_idx, row in enumerate(grid_cells):
            for col_idx, cell in enumerate(row):
                block = cell.get("block")
                if block and block not in ['.', '---', '|', 'X', None]:
                    blocks.append((row_idx, col_idx, block))

                    # Categorize blocks
                    if any(inp in block for inp in REQUIRED_INPUT_BLOCKS):
                        input_blocks.append(block)
                    elif any(out in block for out in REQUIRED_OUTPUT_BLOCKS):
                        output_blocks.append(block)
                    else:
                        effect_blocks.append(block)

        # CRITICAL: Check for required INPUT blocks
        if not input_blocks:
            errors.append("❌ CRITICAL: No INPUT block found! Preset will not receive guitar signal.")
            errors.append("   → Add at least one INPUT block (IN 1, IN 2, etc.)")

        # CRITICAL: Check for required OUTPUT blocks
        if not output_blocks:
            errors.append("❌ CRITICAL: No OUTPUT block found! Preset will not output any sound.")
            errors.append("   → Add at least one OUTPUT block (OUT 1, OUT 2, etc.)")

        # Check for complete signal path
        if input_blocks and output_blocks:
            # Basic signal path validation
            input_positions = [(row, col) for row, col, block in blocks if any(inp in block for inp in REQUIRED_INPUT_BLOCKS)]
            output_positions = [(row, col) for row, col, block in blocks if any(out in block for out in REQUIRED_OUTPUT_BLOCKS)]

            if input_positions and output_positions:
                # Check if input comes before output (basic left-to-right flow)
                leftmost_input = min(input_positions, key=lambda x: x[1])
                rightmost_output = max(output_positions, key=lambda x: x[1])

                if leftmost_input[1] >= rightmost_output[1]:
                    warnings.append("⚠️ Signal flow: INPUT should typically come before OUTPUT in the chain")

        # Check for essential effect blocks
        if not effect_blocks and input_blocks and output_blocks:
            warnings.append("⚠️ No effect blocks found - preset will pass clean signal only")
            suggestions.append("💡 Consider adding: AMP, CAB, DRIVE, DELAY, or REVERB blocks")

        # Check for amp modeling
        amp_blocks = [block for block in effect_blocks if "AMP" in block]
        cab_blocks = [block for block in effect_blocks if "CAB" in block]

        if amp_blocks and not cab_blocks:
            warnings.append("⚠️ AMP block found without CAB block - may sound harsh")
            suggestions.append("💡 Add a CAB block after the AMP for realistic guitar tone")

        # Check for proper gain staging
        drive_blocks = [block for block in effect_blocks if any(d in block for d in ["DRIVE", "BOOST", "FUZZ", "DISTORTION"])]
        if len(drive_blocks) > 2:
            warnings.append("⚠️ Multiple drive blocks detected - watch for excessive gain")
            suggestions.append("💡 Consider using only 1-2 drive blocks for cleaner tone")

        # Overall validation result
        is_valid = len(errors) == 0

        return is_valid, errors, warnings, suggestions

    @staticmethod
    def get_missing_io_blocks(grid_cells):
        """
        Returns lists of missing essential I/O blocks.

        Returns:
            tuple: (missing_inputs: list, missing_outputs: list)
        """
        blocks = []
        for row in grid_cells:
            for cell in row:
                block = cell.get("block")
                if block and block not in ['.', '---', '|', 'X', None]:
                    blocks.append(block)

        # Check for any input/output blocks
        has_input = any(any(inp in block for inp in REQUIRED_INPUT_BLOCKS) for block in blocks)
        has_output = any(any(out in block for out in REQUIRED_OUTPUT_BLOCKS) for block in blocks)

        missing_inputs = [] if has_input else ["IN 1"]
        missing_outputs = [] if has_output else ["OUT 1"]

        return missing_inputs, missing_outputs

    @staticmethod
    def create_basic_signal_path():
        """
        Creates a basic functional signal path with essential I/O blocks.

        Returns:
            list: Basic signal chain with INPUT and OUTPUT
        """
        return ["IN 1", "AMP 1", "CAB 1", "OUT 1"]

class AIEnhancementSystem:
    """
    Enhances AI preset generation with real-time web search and dynamic knowledge updates.
    Provides context-aware information retrieval for better preset quality.
    """

    def __init__(self):
        self.web_search = WebSearchManager()
        self.knowledge_cache = {}
        self.last_search_time = {}

    def enhance_preset_generation(self, query, style, model_name):
        """
        Enhances preset generation with real-time web search information.

        Args:
            query (str): User's preset request
            style (str): Musical style/genre
            model_name (str): AI model being used

        Returns:
            dict: Enhanced context information for AI generation
        """
        enhancement_data = {
            "web_search_results": {},
            "tone_references": {},
            "firmware_info": {},
            "block_compatibility": {},
            "context_summary": "",
            "sources": [],
            "search_performed": False
        }

        try:
            # Extract key terms from user query
            search_terms = self._extract_search_terms(query)

            # Perform context-aware searches
            if search_terms.get("artist"):
                enhancement_data["tone_references"] = self._search_artist_tones(
                    search_terms["artist"], search_terms.get("song")
                )
                enhancement_data["search_performed"] = True

            if search_terms.get("equipment"):
                enhancement_data["equipment_info"] = self._search_equipment_info(
                    search_terms["equipment"]
                )
                enhancement_data["search_performed"] = True

            if search_terms.get("genre") or style:
                genre = search_terms.get("genre", style)
                enhancement_data["genre_info"] = self._search_genre_techniques(genre)
                enhancement_data["search_performed"] = True

            # Get current firmware and block information
            enhancement_data["firmware_info"] = self.web_search.get_current_firmware_info()

            # Create context summary for AI
            enhancement_data["context_summary"] = self._create_context_summary(enhancement_data)

            # Collect all sources
            enhancement_data["sources"] = self._collect_sources(enhancement_data)

        except Exception as e:
            enhancement_data["error"] = str(e)
            enhancement_data["context_summary"] = "Web search enhancement unavailable - using base knowledge only."

        return enhancement_data

    def _extract_search_terms(self, query):
        """Extracts searchable terms from user query."""
        search_terms = {
            "artist": None,
            "song": None,
            "genre": None,
            "equipment": None,
            "effects": []
        }

        query_lower = query.lower()

        # Common artist indicators
        artist_indicators = ["like", "similar to", "sound like", "tone of", "style of"]
        for indicator in artist_indicators:
            if indicator in query_lower:
                # Extract potential artist name after indicator
                parts = query_lower.split(indicator)
                if len(parts) > 1:
                    potential_artist = parts[1].strip().split()[0:3]  # Take up to 3 words
                    search_terms["artist"] = " ".join(potential_artist)
                break

        # Look for song titles in quotes
        song_matches = re.findall(r'"([^"]*)"', query)
        if song_matches:
            search_terms["song"] = song_matches[0]

        # Common equipment terms
        equipment_terms = [
            "marshall", "fender", "gibson", "les paul", "stratocaster", "telecaster",
            "tube screamer", "big muff", "boss", "ibanez", "mesa boogie", "vox"
        ]

        for term in equipment_terms:
            if term in query_lower:
                search_terms["equipment"] = term
                break

        # Genre detection
        genre_terms = [
            "metal", "rock", "blues", "jazz", "country", "pop", "punk", "grunge",
            "progressive", "ambient", "clean", "acoustic", "heavy", "djent", "fusion"
        ]

        for genre in genre_terms:
            if genre in query_lower:
                search_terms["genre"] = genre
                break

        return search_terms

    def _search_artist_tones(self, artist, song=None):
        """Searches for artist-specific tone information."""
        try:
            tone_results = self.web_search.search_tone_references(
                artist=artist, song=song
            )

            if tone_results.get("success"):
                return {
                    "artist": artist,
                    "song": song,
                    "tone_info": tone_results.get("tone_info", []),
                    "search_successful": True
                }
            else:
                return {
                    "artist": artist,
                    "song": song,
                    "search_successful": False,
                    "error": tone_results.get("error", "Unknown error")
                }

        except Exception as e:
            return ErrorHandler.handle_search_error(f"artist_tones_{artist}", e)

    def _search_equipment_info(self, equipment):
        """Searches for equipment-specific information."""
        try:
            equipment_query = f"{equipment} guitar tone settings Axe-Fx III equivalent"
            results = self.web_search.search_fractal_audio_info(equipment_query, "general")

            return {
                "equipment": equipment,
                "search_results": results.get("results", []),
                "search_successful": results.get("success", False)
            }

        except Exception as e:
            return ErrorHandler.handle_search_error(f"equipment_{equipment}", e)

    def _search_genre_techniques(self, genre):
        """Searches for genre-specific techniques and settings."""
        try:
            genre_query = f"{genre} guitar tone techniques Axe-Fx III settings"
            results = self.web_search.search_fractal_audio_info(genre_query, "general")

            return {
                "genre": genre,
                "techniques": results.get("results", []),
                "search_successful": results.get("success", False)
            }

        except Exception as e:
            return ErrorHandler.handle_search_error(f"genre_{genre}", e)

    def _create_context_summary(self, enhancement_data):
        """Creates a summary of web search context for AI generation."""
        summary_parts = []

        # Add tone reference information
        if enhancement_data.get("tone_references", {}).get("search_successful"):
            tone_ref = enhancement_data["tone_references"]
            if tone_ref.get("artist"):
                summary_parts.append(f"ARTIST REFERENCE: {tone_ref['artist']}")
                if tone_ref.get("song"):
                    summary_parts.append(f"SONG REFERENCE: {tone_ref['song']}")

                # Add tone information from search results
                tone_info = tone_ref.get("tone_info", [])
                if tone_info:
                    summary_parts.append("TONE CHARACTERISTICS:")
                    for info in tone_info[:3]:  # Top 3 results
                        summary_parts.append(f"- {info.get('snippet', '')[:100]}...")

        # Add equipment information
        if enhancement_data.get("equipment_info", {}).get("search_successful"):
            equipment_info = enhancement_data["equipment_info"]
            summary_parts.append(f"EQUIPMENT REFERENCE: {equipment_info['equipment']}")

            results = equipment_info.get("search_results", [])
            if results:
                summary_parts.append("EQUIPMENT CHARACTERISTICS:")
                for result in results[:2]:  # Top 2 results
                    summary_parts.append(f"- {result.get('snippet', '')[:100]}...")

        # Add genre information
        if enhancement_data.get("genre_info", {}).get("search_successful"):
            genre_info = enhancement_data["genre_info"]
            summary_parts.append(f"GENRE TECHNIQUES: {genre_info['genre']}")

            techniques = genre_info.get("techniques", [])
            if techniques:
                for technique in techniques[:2]:  # Top 2 results
                    summary_parts.append(f"- {technique.get('snippet', '')[:100]}...")

        # Add firmware information
        firmware_info = enhancement_data.get("firmware_info", {})
        if firmware_info.get("success"):
            fw_details = firmware_info.get("firmware_info", {})
            if fw_details.get("version") != "Unknown":
                summary_parts.append(f"CURRENT FIRMWARE: {fw_details['version']}")

        if summary_parts:
            return "REAL-TIME WEB SEARCH CONTEXT:\n" + "\n".join(summary_parts)
        else:
            return "No additional web context available - using base knowledge."

    def _collect_sources(self, enhancement_data):
        """Collects all source URLs from search results."""
        sources = []

        # Collect from tone references
        tone_info = enhancement_data.get("tone_references", {}).get("tone_info", [])
        for info in tone_info:
            if info.get("url"):
                sources.append(info["url"])

        # Collect from equipment info
        equipment_results = enhancement_data.get("equipment_info", {}).get("search_results", [])
        for result in equipment_results:
            if result.get("url"):
                sources.append(result["url"])

        # Collect from genre info
        genre_techniques = enhancement_data.get("genre_info", {}).get("techniques", [])
        for technique in genre_techniques:
            if technique.get("url"):
                sources.append(technique["url"])

        # Remove duplicates
        return list(set(sources))

class AxeFxPresetGeneratorPro(ctk.CTk):
    """
    Professional version of Axe-Fx III Preset Generator with advanced features.
    """
    
    def __init__(self):
        super().__init__()

        # Initialize secure API key manager
        self.api_key_manager = SecureAPIKeyManager()

        # Initialize AI enhancement system with web search capabilities
        self.ai_enhancement = AIEnhancementSystem()

        # Basic window settings
        self.title("Axe-Fx III Preset Generator Professional - Enhanced with Web Search")
        self.geometry("1600x1000")
        self.minsize(1400, 900)

        # Theme and styles
        ctk.set_appearance_mode("Dark")
        ctk.set_default_color_theme("blue")

        # Layout
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=1)

        # Main container with tabs
        self.create_main_interface()

        # Data initialization
        self.current_preset = {
            "name": "",
            "grid": [[None for _ in range(GRID_COLS)] for _ in range(GRID_ROWS)],
            "connections": [],
            "scenes": self.initialize_scenes(),
            "cpu_usage": 0,
            "metadata": {},
            "blocks": {}  # Detailed info about each block including channels
        }

        self.preset_history = []
        self.cpu_meter_active = False

        # Load saved API key after UI is created
        self.after(100, self.load_saved_api_key)
        
    def initialize_scenes(self):
        """Initializes 8 scenes with default settings."""
        scenes = []
        for i in range(8):
            scenes.append({
                "name": f"Scene {i+1}",
                "blocks": {},  # block_id: {"bypass": bool, "channel": "A/B/C/D"}
                "tempo": 120,
                "controllers": {}
            })
        return scenes
        
    def create_main_interface(self):
        """Creates the main interface with tabs."""
        # Main notebook for tabs
        self.notebook = ttk.Notebook(self)
        self.notebook.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        
        # Tab 1: Generator
        self.generator_tab = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.generator_tab, text="Preset Generator")
        self.create_generator_tab()
        
        
        # Tab 3: Templates
        self.templates_tab = ctk.CTkFrame(self.notebook)
        self.notebook.add(self.templates_tab, text="Templates & Artists")
        self.create_templates_tab()
        
        # Status bar
        self.create_status_bar()
        
    def create_generator_tab(self):
        """Creates the Generator tab content."""
        self.generator_tab.grid_columnconfigure(0, weight=5, minsize=700)
        self.generator_tab.grid_columnconfigure(1, weight=4)
        self.generator_tab.grid_rowconfigure(0, weight=1)
        
        # Left panel
        left_panel = ctk.CTkFrame(self.generator_tab)
        left_panel.grid(row=0, column=0, padx=(10, 5), pady=10, sticky="nsew")
        left_panel.grid_rowconfigure(2, weight=1)
        left_panel.grid_columnconfigure(0, weight=1)
        
        # Input section
        self.create_input_section(left_panel)
        
        # CPU Monitor
        self.create_cpu_monitor(left_panel)
        
        # Grid visualization
        self.create_grid_visualization(left_panel)
        
        # Right panel
        right_panel = ctk.CTkFrame(self.generator_tab)
        right_panel.grid(row=0, column=1, padx=(5, 10), pady=10, sticky="nsew")
        right_panel.grid_rowconfigure(1, weight=1)
        right_panel.grid_columnconfigure(0, weight=1)
        
        # Output section
        self.create_output_section(right_panel)
        
    def create_input_section(self, parent):
        """Creates the input section with advanced options."""
        input_frame = ctk.CTkFrame(parent)
        input_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        input_frame.grid_columnconfigure(1, weight=1)
        
        # API Key section
        ctk.CTkLabel(input_frame, text="API Key:").grid(
            row=0, column=0, padx=10, pady=5, sticky="w"
        )
        self.api_key_entry = ctk.CTkEntry(
            input_frame,
            placeholder_text="Enter Google API key...",
            show="*"
        )
        self.api_key_entry.grid(row=0, column=1, padx=(0, 5), pady=5, sticky="ew")

        # API Key management buttons frame
        key_buttons_frame = ctk.CTkFrame(input_frame)
        key_buttons_frame.grid(row=0, column=2, padx=(5, 10), pady=5, sticky="ew")

        self.save_key_button = ctk.CTkButton(
            key_buttons_frame,
            text="Save",
            command=self.save_api_key,
            width=60,
            height=28
        )
        self.save_key_button.pack(side="left", padx=2)

        self.clear_key_button = ctk.CTkButton(
            key_buttons_frame,
            text="Clear",
            command=self.clear_api_key,
            width=60,
            height=28
        )
        self.clear_key_button.pack(side="left", padx=2)

        self.load_models_button = ctk.CTkButton(
            key_buttons_frame,
            text="Load Models",
            command=self.load_models,
            width=80,
            height=28
        )
        self.load_models_button.pack(side="left", padx=2)

        # API Key status indicator
        self.api_key_status = ctk.CTkLabel(
            input_frame,
            text="",
            font=ctk.CTkFont(size=11),
            text_color="#888888"
        )
        self.api_key_status.grid(row=0, column=3, padx=5, pady=5, sticky="w")

        # Web search enhancement controls
        web_search_frame = ctk.CTkFrame(input_frame)
        web_search_frame.grid(row=1, column=0, columnspan=4, padx=10, pady=5, sticky="ew")

        self.web_search_enabled = ctk.CTkCheckBox(
            web_search_frame,
            text="Enable Real-time Web Search Enhancement",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.web_search_enabled.pack(side="left", padx=10, pady=5)
        self.web_search_enabled.select()  # Enable by default

        self.web_search_status = ctk.CTkLabel(
            web_search_frame,
            text="🌐 Web search ready - will enhance presets with current information",
            font=ctk.CTkFont(size=10),
            text_color="#4CAF50"
        )
        self.web_search_status.pack(side="left", padx=20, pady=5)
        
        # Model selection
        ctk.CTkLabel(input_frame, text="AI Model:").grid(
            row=1, column=0, padx=10, pady=5, sticky="w"
        )
        self.model_combobox = ctk.CTkComboBox(
            input_frame,
            values=["Load models..."],
            state="disabled"
        )
        self.model_combobox.grid(row=1, column=1, columnspan=2, padx=(0, 10), pady=5, sticky="ew")
        
        # Style selection
        ctk.CTkLabel(input_frame, text="Style:").grid(
            row=2, column=0, padx=10, pady=5, sticky="w"
        )
        self.style_combobox = ctk.CTkComboBox(
            input_frame,
            values=list(EFFECT_CHAINS.keys()),
            command=self.on_style_change
        )
        self.style_combobox.set("Metal")
        self.style_combobox.grid(row=2, column=1, columnspan=2, padx=(0, 10), pady=5, sticky="ew")
        
        # Advanced options
        self.advanced_frame = ctk.CTkFrame(input_frame)
        self.advanced_frame.grid(row=3, column=0, columnspan=3, sticky="ew", padx=10, pady=5)
        
        self.parallel_routing_var = ctk.BooleanVar(value=False)
        ctk.CTkCheckBox(
            self.advanced_frame,
            text="Parallel routing",
            variable=self.parallel_routing_var
        ).pack(side="left", padx=5)
        
        self.scenes_var = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(
            self.advanced_frame,
            text="Generate scenes",
            variable=self.scenes_var
        ).pack(side="left", padx=5)
        
        self.spillover_var = ctk.BooleanVar(value=True)
        ctk.CTkCheckBox(
            self.advanced_frame,
            text="Spillover",
            variable=self.spillover_var
        ).pack(side="left", padx=5)
        
        # Query input
        ctk.CTkLabel(input_frame, text="Description of desired sound:").grid(
            row=4, column=0, columnspan=3, padx=10, pady=(10, 5), sticky="w"
        )
        
        self.query_textbox = ctk.CTkTextbox(input_frame, height=100)
        self.query_textbox.grid(row=5, column=0, columnspan=3, padx=10, pady=(0, 10), sticky="ew")
        
        # Generate button
        self.generate_button = ctk.CTkButton(
            input_frame,
            text="Generate Preset",
            command=self.generate_preset,
            height=40,
            font=ctk.CTkFont(size=16, weight="bold"),
            state="disabled"
        )
        self.generate_button.grid(row=6, column=0, columnspan=3, padx=10, pady=10, sticky="ew")
        
    def create_cpu_monitor(self, parent):
        """Creates the CPU monitor widget."""
        cpu_frame = ctk.CTkFrame(parent)
        cpu_frame.grid(row=1, column=0, sticky="ew", pady=10)
        
        ctk.CTkLabel(
            cpu_frame,
            text="CPU Usage Monitor",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=5)
        
        # CPU progress bar
        self.cpu_progress = ctk.CTkProgressBar(cpu_frame, height=20)
        self.cpu_progress.pack(padx=20, pady=5, fill="x")
        self.cpu_progress.set(0)
        
        # CPU label
        self.cpu_label = ctk.CTkLabel(
            cpu_frame,
            text="CPU: 0% / 90% limit",
            font=ctk.CTkFont(size=12)
        )
        self.cpu_label.pack()
        
        # Warning label
        self.cpu_warning = ctk.CTkLabel(
            cpu_frame,
            text="",
            text_color="orange",
            font=ctk.CTkFont(size=11)
        )
        self.cpu_warning.pack()
        
    def create_grid_visualization(self, parent):
        """Creates Fractal Audio compliant grid visualization."""
        grid_frame = ctk.CTkFrame(parent)
        grid_frame.grid(row=2, column=0, sticky="nsew")
        grid_frame.grid_columnconfigure(0, weight=1)
        grid_frame.grid_rowconfigure(1, weight=1)

        # Header with Fractal Audio branding
        header_frame = ctk.CTkFrame(grid_frame)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=(10, 5))

        ctk.CTkLabel(
            header_frame,
            text="Fractal Audio Signal Path Visualization",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(side="left", padx=10)

        # Signal flow indicator
        flow_label = ctk.CTkLabel(
            header_frame,
            text="INPUT → DYNAMICS → DRIVE → ROUTING → AMP → CAB → EQ → MOD → DELAY → REVERB → OUTPUT",
            font=ctk.CTkFont(size=10),
            text_color="#888888"
        )
        flow_label.pack(side="left", padx=20)

        # Grid controls
        ctk.CTkButton(
            header_frame,
            text="Validate Flow",
            command=self.validate_signal_flow_visual,
            width=100
        ).pack(side="right", padx=5)

        ctk.CTkButton(
            header_frame,
            text="Clear",
            command=self.clear_grid,
            width=60
        ).pack(side="right", padx=5)

        ctk.CTkButton(
            header_frame,
            text="Auto-route",
            command=self.auto_route,
            width=80
        ).pack(side="right", padx=5)

        # Grid container with scrollbar
        grid_container = ctk.CTkFrame(grid_frame)
        grid_container.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))

        # Canvas for grid
        self.grid_canvas = tk.Canvas(
            grid_container,
            bg="#1a1a1a",
            highlightthickness=0
        )
        self.grid_canvas.pack(fill="both", expand=True)

        # Creating grid cells and connections
        self.grid_cells = []
        self.grid_connections = []
        self.create_fractal_grid_cells()
        
    def create_fractal_grid_cells(self):
        """Creates Fractal Audio compliant grid cells with proper signal flow visualization."""
        cell_size = 70
        padding = 3
        connection_width = 20

        for row in range(GRID_ROWS):
            row_cells = []
            for col in range(GRID_COLS):
                x1 = col * (cell_size + padding + connection_width) + 10
                y1 = row * (cell_size + padding) + 10
                x2 = x1 + cell_size
                y2 = y1 + cell_size

                # Creating cell with Fractal Audio styling
                rect = self.grid_canvas.create_rectangle(
                    x1, y1, x2, y2,
                    fill=BLOCK_COLORS["empty"],
                    outline="#555555",
                    width=2
                )

                # Text in cell
                text = self.grid_canvas.create_text(
                    x1 + cell_size/2,
                    y1 + cell_size/2,
                    text="",
                    fill="white",
                    font=("Arial", 8, "bold"),
                    width=cell_size-10
                )

                # Signal flow position indicator (small text below cell)
                flow_indicator = self.grid_canvas.create_text(
                    x1 + cell_size/2,
                    y2 + 15,
                    text="",
                    fill="#666666",
                    font=("Arial", 7)
                )

                # Connection line to next cell (if not last column)
                connection_line = None
                if col < GRID_COLS - 1:
                    line_x1 = x2 + 5
                    line_y1 = y1 + cell_size/2
                    line_x2 = line_x1 + connection_width - 10
                    line_y2 = line_y1

                    connection_line = self.grid_canvas.create_line(
                        line_x1, line_y1, line_x2, line_y2,
                        fill="#333333",
                        width=2,
                        state="hidden"  # Initially hidden
                    )

                row_cells.append({
                    "rect": rect,
                    "text": text,
                    "flow_indicator": flow_indicator,
                    "connection": connection_line,
                    "block": None,
                    "signal_flow_position": None,
                    "is_valid_position": True
                })

            self.grid_cells.append(row_cells)

        # Setting canvas size with space for connections
        canvas_width = GRID_COLS * (cell_size + padding + connection_width) + 20
        canvas_height = GRID_ROWS * (cell_size + padding) + 50  # Extra space for flow indicators
        self.grid_canvas.config(width=canvas_width, height=canvas_height)
        
    def create_output_section(self, parent):
        """Creates the output section with detailed instructions."""
        # Header
        header_frame = ctk.CTkFrame(parent)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=(0, 10))
        
        ctk.CTkLabel(
            header_frame,
            text="Detailed Preset Instructions",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(side="left")
        
        # Export buttons
        ctk.CTkButton(
            header_frame,
            text="Export Preset",
            command=self.export_preset,
            width=100
        ).pack(side="right", padx=5)
        
        ctk.CTkButton(
            header_frame,
            text="Search Online",
            command=self.search_online_presets,
            width=100
        ).pack(side="right", padx=5)
        
        ctk.CTkButton(
            header_frame,
            text="Copy",
            command=self.copy_to_clipboard,
            width=80
        ).pack(side="right", padx=5)
        
        # Output text with formatting
        self.output_text = tk.Text(
            parent,
            wrap="word",
            bg="#2b2b2b",
            fg="#ffffff",
            font=("Consolas", 12),
            padx=10,
            pady=10,
            insertbackground="white"
        )
        self.output_text.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(parent, command=self.output_text.yview)
        scrollbar.grid(row=1, column=1, sticky="ns", pady=(0, 10))
        self.output_text.config(yscrollcommand=scrollbar.set)
        
        # Text formatting tags
        self.setup_text_tags()
        
    def setup_text_tags(self):
        """Sets up text formatting tags."""
        self.output_text.tag_config(
            "heading1",
            font=("Arial", 18, "bold"),
            foreground="#4FC3F7",
            spacing3=10
        )
        self.output_text.tag_config(
            "heading2", 
            font=("Arial", 14, "bold"),
            foreground="#81C784",
            spacing3=8
        )
        self.output_text.tag_config(
            "block_name",
            font=("Consolas", 12, "bold"),
            foreground="#FFB74D"
        )
        self.output_text.tag_config(
            "parameter",
            font=("Consolas", 11),
            foreground="#CE93D8"
        )
        self.output_text.tag_config(
            "value",
            font=("Consolas", 11, "bold"),
            foreground="#A5D6A7"
        )
        
    def create_templates_tab(self):
        """Creates the Templates & Artists tab."""
        self.templates_tab.grid_columnconfigure(0, weight=1)
        self.templates_tab.grid_columnconfigure(1, weight=1)
        self.templates_tab.grid_rowconfigure(0, weight=1)
        
        # Left panel - Templates
        templates_frame = ctk.CTkFrame(self.templates_tab)
        templates_frame.grid(row=0, column=0, sticky="nsew", padx=(10, 5), pady=10)
        
        ctk.CTkLabel(
            templates_frame,
            text="Preset Templates",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)
        
        # Template list
        template_scroll = ctk.CTkScrollableFrame(templates_frame)
        template_scroll.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        templates = {
            "High Gain Rhythm": {
                "chain": ["GATE", "DRV", "AMP", "CAB", "GEQ", "DLY", "REV"],
                "description": "Tight metal rhythm with noise gate"
            },
            "Lead Tone": {
                "chain": ["CMP", "DRV", "AMP", "CAB", "DLY", "REV"],
                "description": "Sustain for solos with compression"
            },
            "Clean Ambient": {
                "chain": ["CMP", "CHO", "AMP", "CAB", "MULTIDLY", "HALL"],
                "description": "Spacious clean sound"
            },
            "Blues Crunch": {
                "chain": ["DRV", "AMP", "CAB", "TREM", "SPRING"],
                "description": "Classic blues tone"
            },
            "Modern Djent": {
                "chain": ["GATE", "DRV", "AMP", "CAB", "GEQ", "CMP"],
                "description": "Ultra-tight progressive metal"
            }
        }
        
        for name, data in templates.items():
            frame = ctk.CTkFrame(template_scroll)
            frame.pack(fill="x", pady=5)
            
            ctk.CTkLabel(
                frame,
                text=name,
                font=ctk.CTkFont(size=14, weight="bold")
            ).pack(anchor="w", padx=10, pady=(5, 0))
            
            ctk.CTkLabel(
                frame,
                text=data["description"],
                font=ctk.CTkFont(size=11),
                text_color="#888888"
            ).pack(anchor="w", padx=10)
            
            ctk.CTkButton(
                frame,
                text="Apply Template",
                command=lambda t=data["chain"]: self.apply_template(t),
                width=120,
                height=28
            ).pack(anchor="e", padx=10, pady=5)
            
        # Right panel - Artist Presets
        artists_frame = ctk.CTkFrame(self.templates_tab)
        artists_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 10), pady=10)
        
        ctk.CTkLabel(
            artists_frame,
            text="Artist Presets",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=10)
        
        # Artist list
        artist_scroll = ctk.CTkScrollableFrame(artists_frame)
        artist_scroll.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        for artist, data in ARTIST_PRESETS.items():
            frame = ctk.CTkFrame(artist_scroll)
            frame.pack(fill="x", pady=5)
            
            ctk.CTkLabel(
                frame,
                text=artist,
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="#FFB74D"
            ).pack(anchor="w", padx=10, pady=(5, 0))
            
            # Amp info
            amp_text = "Amps: " + ", ".join(data["amps"])
            ctk.CTkLabel(
                frame,
                text=amp_text,
                font=ctk.CTkFont(size=11),
                text_color="#A5D6A7"
            ).pack(anchor="w", padx=10)
            
            # Other info
            if "drive" in data:
                ctk.CTkLabel(
                    frame,
                    text=f"Drive: {data['drive']}",
                    font=ctk.CTkFont(size=11),
                    text_color="#888888"
                ).pack(anchor="w", padx=10)
                
            ctk.CTkButton(
                frame,
                text="Load Preset",
                command=lambda a=artist: self.load_artist_preset(a),
                width=120,
                height=28
            ).pack(anchor="e", padx=10, pady=5)
            
    def create_status_bar(self):
        """Creates the status bar."""
        self.status_frame = ctk.CTkFrame(self, height=30)
        self.status_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=(0, 10))
        self.status_frame.grid_columnconfigure(1, weight=1)
        
        # Status label
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Ready",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.grid(row=0, column=0, padx=10)
        
        # Preset name
        self.preset_name_label = ctk.CTkLabel(
            self.status_frame,
            text="",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.preset_name_label.grid(row=0, column=1, padx=10)
        
        # Info labels
        self.info_label = ctk.CTkLabel(
            self.status_frame,
            text="",
            font=ctk.CTkFont(size=11),
            text_color="#888888"
        )
        self.info_label.grid(row=0, column=2, padx=10)
        
    # === API Key Management Functions ===

    def load_saved_api_key(self):
        """Loads saved API key on application startup."""
        try:
            api_key, method, error = self.api_key_manager.load_api_key()

            if api_key:
                self.api_key_entry.delete(0, "end")
                self.api_key_entry.insert(0, api_key)

                # Update status
                method_name = "OS Keyring" if method == "keyring" else "Encrypted File"
                self.api_key_status.configure(
                    text=f"✓ Loaded from {method_name}",
                    text_color="#4CAF50"
                )

                # Automatically load models if key is available
                self.after(500, self.auto_load_models)

            else:
                is_saved, methods = self.api_key_manager.is_api_key_saved()
                if is_saved:
                    self.api_key_status.configure(
                        text="⚠ Key found but couldn't load",
                        text_color="#FF9800"
                    )
                else:
                    self.api_key_status.configure(
                        text="No saved key",
                        text_color="#888888"
                    )

        except Exception as e:
            self.api_key_status.configure(
                text="⚠ Error loading key",
                text_color="#F44336"
            )

    def save_api_key(self):
        """Saves the current API key securely."""
        api_key = self.api_key_entry.get().strip()

        if not api_key:
            self.show_error("Please enter an API key first")
            return

        # Validate API key format (basic check)
        if not self.validate_api_key_format(api_key):
            response = messagebox.askyesno(
                "Invalid Format",
                "The API key format doesn't look correct. Save anyway?"
            )
            if not response:
                return

        try:
            success, method, error = self.api_key_manager.save_api_key(api_key)

            if success:
                method_name = "OS Keyring" if method == "keyring" else "Encrypted File"
                self.api_key_status.configure(
                    text=f"✓ Saved to {method_name}",
                    text_color="#4CAF50"
                )
                self.update_status(f"API key saved securely using {method_name}", "green")

                # Show security info
                self.show_security_info(method)

            else:
                self.show_error(f"Failed to save API key: {error}")
                self.api_key_status.configure(
                    text="✗ Save failed",
                    text_color="#F44336"
                )

        except Exception as e:
            self.show_error(f"Error saving API key: {str(e)}")

    def clear_api_key(self):
        """Clears saved API key from secure storage."""
        response = messagebox.askyesno(
            "Clear Saved API Key",
            "This will remove the saved API key from secure storage.\n\n"
            "You will need to enter it again next time.\n\n"
            "Continue?"
        )

        if not response:
            return

        try:
            success, methods_cleared, errors = self.api_key_manager.clear_api_key()

            if success:
                self.api_key_status.configure(
                    text="Cleared",
                    text_color="#888888"
                )

                if methods_cleared:
                    methods_text = ", ".join([
                        "OS Keyring" if m == "keyring" else "Encrypted File"
                        for m in methods_cleared
                    ])
                    self.update_status(f"API key cleared from: {methods_text}", "green")
                else:
                    self.update_status("No saved API key found", "yellow")

            if errors:
                error_text = "; ".join(errors)
                self.show_error(f"Some errors occurred: {error_text}")

        except Exception as e:
            self.show_error(f"Error clearing API key: {str(e)}")

    def validate_api_key_format(self, api_key):
        """Basic validation of API key format."""
        # Google API keys typically start with 'AIza' and are 39 characters long
        if api_key.startswith('AIza') and len(api_key) == 39:
            return True
        # But allow other formats too (API keys can vary)
        return len(api_key) > 20

    def show_security_info(self, method):
        """Shows information about the security method used."""
        if method == "keyring":
            info_text = (
                "API Key Security Information\n\n"
                "✓ Your API key has been saved to your system's secure credential storage:\n"
                "  • Windows: Windows Credential Manager\n"
                "  • macOS: Keychain\n"
                "  • Linux: Secret Service\n\n"
                "This is the most secure method available and integrates with your OS security."
            )
        else:
            info_text = (
                "API Key Security Information\n\n"
                "✓ Your API key has been saved to an encrypted local file.\n\n"
                "Security features:\n"
                "  • AES encryption with machine-specific key\n"
                "  • Key derivation using PBKDF2 (100,000 iterations)\n"
                "  • File is only readable by this application\n\n"
                "Note: OS Keyring wasn't available, using encrypted file as fallback."
            )

        messagebox.showinfo("Security Information", info_text)

    def auto_load_models(self):
        """Automatically loads models if API key is available."""
        if self.api_key_entry.get().strip():
            self.load_models()

    # === Main Functions ===

    def load_models(self):
        """Loads available AI models."""
        api_key = self.api_key_entry.get()
        if not api_key:
            self.show_error("Please enter API key")
            return
            
        self.load_models_button.configure(state="disabled", text="Loading...")
        self.update_status("Loading models...")
        
        thread = threading.Thread(target=self._load_models_thread)
        thread.daemon = True
        thread.start()
        
    def _load_models_thread(self):
        """Thread for loading models."""
        try:
            genai.configure(api_key=self.api_key_entry.get())
            models = [m.name for m in genai.list_models() 
                     if 'generateContent' in m.supported_generation_methods]
            
            self.after(0, self._update_models_ui, sorted(models, reverse=True))
        except Exception as e:
            self.after(0, self.show_error, f"Error loading models: {str(e)}")
            self.after(0, lambda: self.load_models_button.configure(
                state="normal", text="Load Models"
            ))
            
    def _update_models_ui(self, models):
        """Updates UI with loaded models."""
        if models:
            self.model_combobox.configure(values=models, state="readonly")
            self.model_combobox.set(models[0])
            self.generate_button.configure(state="normal")
            self.update_status("Models loaded", "green")
        else:
            self.show_error("No models found")
            
        self.load_models_button.configure(state="normal", text="Load Models")
        
    def generate_preset(self):
        """Starts preset generation."""
        query = self.query_textbox.get("1.0", "end").strip()
        if not query:
            self.show_error("Enter description of desired sound")
            return
            
        self.generate_button.configure(state="disabled", text="Generating...")
        self.update_status("Generating preset...")
        self.clear_grid()
        self.output_text.delete("1.0", "end")
        
        # Start CPU monitoring
        self.start_cpu_monitoring()
        
        thread = threading.Thread(target=self._generate_preset_thread)
        thread.daemon = True
        thread.start()
        
    def _generate_preset_thread(self):
        """Thread for preset generation."""
        try:
            model = genai.GenerativeModel(self.model_combobox.get())
            prompt = self.create_advanced_prompt()
            
            response = model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.3,
                    top_p=0.9,
                    top_k=40
                )
            )
            
            self.after(0, self.process_response, response.text)
        except Exception as e:
            self.after(0, self.show_error, f"Error during generation: {str(e)}")
            self.after(0, lambda: self.generate_button.configure(
                state="normal", text="Generate Preset"
            ))
            self.after(0, self.stop_cpu_monitoring)
            
    def validate_gain_staging(self, blocks):
        """Validates gain staging between blocks."""
        warnings = []
        
        for i in range(len(blocks) - 1):
            current = blocks[i]
            next_block = blocks[i + 1]
            
            # Check for unity gain between blocks
            if "DRIVE" in current and "AMP" in next_block:
                warnings.append("⚠️ Drive → Amp: Set Drive Level to ~5 for unity gain")
            
            if "AMP" in current and "CAB" in next_block:
                warnings.append("ℹ️ Amp → Cab: Master Volume affects tone, recommended 5-7")
                
            if "COMPRESSOR" in current:
                warnings.append("⚠️ Compressor: Threshold and Ratio affect overall gain")
                
        return warnings

    def validate_and_display_preset_status(self):
        """Validates the current preset using official Fractal Audio standards."""
        # Use both validators for comprehensive checking
        preset_valid, preset_errors, preset_warnings, preset_suggestions = PresetValidator.validate_preset(self.grid_cells)
        flow_valid, flow_errors, flow_warnings, flow_suggestions = FractalGridManager.validate_signal_flow(self.grid_cells)

        # Combine results
        is_valid = preset_valid and flow_valid
        all_errors = preset_errors + flow_errors
        all_warnings = preset_warnings + flow_warnings
        all_suggestions = preset_suggestions + flow_suggestions

        # Always show validation results
        self.output_text.insert("end", "\n\n=== FRACTAL AUDIO PRESET VALIDATION ===\n", "heading2")

        if is_valid:
            self.output_text.insert("end", "✅ PRESET FOLLOWS FRACTAL AUDIO STANDARDS\n", "value")
            self.output_text.insert("end", "   • Official signal flow order maintained\n", "parameter")
            self.output_text.insert("end", "   • INPUT and OUTPUT blocks present\n", "parameter")
            self.output_text.insert("end", "   • Ready for Axe-Fx III hardware!\n", "parameter")
        else:
            self.output_text.insert("end", "❌ PRESET VALIDATION FAILED\n", "heading2")
            self.output_text.insert("end", "   This preset does NOT follow Fractal Audio standards!\n", "parameter")
            self.output_text.insert("end", "   May not work properly on Axe-Fx III hardware.\n\n", "parameter")

        # Show errors (critical issues)
        if all_errors:
            self.output_text.insert("end", "\n🚨 CRITICAL ERRORS (Must Fix):\n", "heading2")
            for error in all_errors:
                self.output_text.insert("end", f"{error}\n", "parameter")

        # Show warnings
        if all_warnings:
            self.output_text.insert("end", "\n⚠️ FRACTAL AUDIO BEST PRACTICES:\n", "heading2")
            for warning in all_warnings:
                self.output_text.insert("end", f"{warning}\n", "parameter")

        # Show suggestions
        if all_suggestions:
            self.output_text.insert("end", "\n💡 OPTIMIZATION SUGGESTIONS:\n", "heading2")
            for suggestion in all_suggestions:
                self.output_text.insert("end", f"{suggestion}\n", "parameter")

        # If preset is invalid, show how to fix it
        if not is_valid:
            missing_inputs, missing_outputs = PresetValidator.get_missing_io_blocks(self.grid_cells)

            self.output_text.insert("end", "\n🔧 FRACTAL AUDIO COMPLIANCE FIXES:\n", "heading2")
            if missing_inputs:
                self.output_text.insert("end", f"1. Add INPUT block: {', '.join(missing_inputs)} (MANDATORY)\n", "parameter")
            if missing_outputs:
                self.output_text.insert("end", f"2. Add OUTPUT block: {', '.join(missing_outputs)} (MANDATORY)\n", "parameter")
            self.output_text.insert("end", "3. Follow official signal flow order: INPUT→DYNAMICS→DRIVE→AMP→CAB→EQ→MOD→DELAY→REVERB→OUTPUT\n", "parameter")
            self.output_text.insert("end", "4. Use Auto-route for proper Fractal Audio layout\n", "parameter")
            self.output_text.insert("end", "5. Re-validate against Fractal standards\n", "parameter")

            # Update status to show validation failure
            self.update_status("⚠️ Preset fails Fractal Audio standards", "red")
        else:
            self.update_status("✅ Preset meets Fractal Audio standards", "green")

    def create_advanced_prompt(self):
        """Creates advanced prompt for AI with web search enhancement."""
        query = self.query_textbox.get("1.0", "end").strip()
        style = self.style_combobox.get()
        model_name = self.model_combobox.get()

        # Get web search enhancement if enabled
        web_enhancement = None
        if self.web_search_enabled.get():
            try:
                self.web_search_status.configure(
                    text="🔍 Searching web for current information...",
                    text_color="#FF9800"
                )
                self.update()  # Update UI

                web_enhancement = self.ai_enhancement.enhance_preset_generation(
                    query, style, model_name
                )

                if web_enhancement.get("search_performed"):
                    self.web_search_status.configure(
                        text="✅ Web search completed - enhanced context available",
                        text_color="#4CAF50"
                    )
                else:
                    self.web_search_status.configure(
                        text="ℹ️ Using base knowledge - no web enhancement needed",
                        text_color="#2196F3"
                    )

            except Exception as e:
                self.web_search_status.configure(
                    text="⚠️ Web search unavailable - using base knowledge",
                    text_color="#FF6B6B"
                )
                web_enhancement = None

        # Store web enhancement for display
        self.last_web_enhancement = web_enhancement

        # Getting relevant information
        effect_chain = EFFECT_CHAINS.get(style, [])
        amp_suggestions = AMP_MODELS.get(
            "High Gain" if style in ["Metal", "Djent"] else 
            "Blues/Rock" if style in ["Rock", "Blues"] else
            "Clean", []
        )
        
        # Build enhanced prompt with web search context
        base_prompt = f"""You are an expert on Fractal Audio Axe-Fx III following OFFICIAL Fractal Audio procedures and best practices.

REQUEST: "{query}"
STYLE: {style}
OFFICIAL FRACTAL SIGNAL FLOW: IN 1 → {' → '.join(effect_chain)} → OUT 1"""

        # Add web search enhancement context if available
        if web_enhancement and web_enhancement.get("context_summary"):
            enhanced_context = f"""

🌐 REAL-TIME WEB SEARCH ENHANCEMENT:
{web_enhancement['context_summary']}

SOURCES CONSULTED:
{chr(10).join([f"• {source}" for source in web_enhancement.get('sources', [])[:5]])}

Use this current information to enhance your preset generation with up-to-date knowledge about tones, techniques, and equipment."""

            base_prompt += enhanced_context

        prompt = base_prompt + f"""

🚨 CRITICAL - OFFICIAL FRACTAL AUDIO REQUIREMENTS:
EVERY preset MUST follow the official Fractal Audio signal flow order:

⚠️ MANDATORY BLOCKS - PRESET WILL NOT WORK WITHOUT THESE:
1. INPUT (IN 1, IN 2, etc.) - MANDATORY FIRST BLOCK - NO EXCEPTIONS!
10. OUTPUT (OUT 1, OUT 2, etc.) - MANDATORY LAST BLOCK - NO EXCEPTIONS!

SIGNAL FLOW ORDER (all blocks between INPUT and OUTPUT):
2. DYNAMICS (Gate/Compressor) - Before drive for clean gating
3. DRIVE (Overdrive/Distortion) - Before amp for proper saturation
4. ROUTING (Send/Return) - For 4-cable method if needed
5. AMP (Amp modeling) - Core tone generation
6. CAB (Cabinet simulation) - Essential for realistic guitar tone
7. EQ (Parametric/Graphic EQ) - Tone shaping after amp/cab
8. MODULATION (Chorus/Flanger/Phaser) - Time-based modulation
9. DELAY (All delay types) - Echo effects
10. REVERB (All reverb types) - Ambient effects

🚨 CRITICAL: Your grid MUST start with IN 1 and end with OUT 1 or the preset will be SILENT!
This order is per official Fractal Audio documentation and ensures optimal CPU usage and signal quality.

STRICT REQUIREMENTS:

1. GRID LAYOUT (14x6):
Create a COMPLETE text representation using exactly these tokens:
- Use ONLY these real Axe-Fx III blocks:
  * I/O: IN 1, IN 2, IN 3, IN 4, IN 5, OUT 1, OUT 2, OUT 3, OUT 4
  * Amps: AMP 1, AMP 2
  * Cabs: CAB 1, CAB 2  
  * Dynamics: GATE/EXP, COMPRESSOR, MULTIBAND COMP, LIMITER
  * Drive: DRIVE, BOOST, FUZZ, DISTORTION
  * Delay: DELAY, MULTI DELAY, PLEX DELAY, MEGATAP DELAY, DUCKING DELAY, TAPE DELAY
  * Reverb: REVERB, SPRING, HALL, PLATE, ROOM
  * Modulation: CHORUS, FLANGER, PHASER, ROTARY, TREMOLO, RING MOD, VIBE
  * Pitch: PITCH, HARMONIZER, WHAMMY, CRYSTALS
  * EQ: PEQ, GEQ, FILTER, TONE MATCH
  * Special: WAH, FORMANT, VOCODER, SYNTH, RESONATOR
  * Utility: VOL/PAN, MIXER, SEND, RETURN, LOOPER, CROSSOVER
- Connections: --- (horizontal), | (vertical), X (crossing)
- Empty: . (dot)

IMPORTANT: Each row MUST have exactly 14 positions!

🚨 MANDATORY FRACTAL AUDIO GRID LAYOUT for {style}:
```grid
IN 1 --- GATE/EXP --- DRIVE --- AMP 1 --- CAB 1 --- GEQ --- CHORUS --- DELAY --- REVERB --- OUT 1 --- . --- . --- . --- .
. --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- .
. --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- .
. --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- .
. --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- .
. --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- . --- .
```

🚨 CRITICAL REQUIREMENTS SHOWN:
• MUST start with IN 1 (INPUT block) - FIRST POSITION
• MUST end with OUT 1 (OUTPUT block) - LAST POSITION
• Left-to-right signal flow following official Fractal Audio order
• Horizontal connections (---) linking blocks in sequence
• Clean, logical layout without crossing connections

⚠️ WARNING: If you don't include IN 1 and OUT 1, the preset will be COMPLETELY SILENT!
🎯 FRACTAL AUDIO OFFICIAL ORDER: INPUT → DYNAMICS → DRIVE → AMP → CAB → EQ → MODULATION → DELAY → REVERB → OUTPUT

2. DETAILED SETTINGS FOR EACH BLOCK:

**GATE/EXP Block**:
- Threshold: -50 to -40 dB (metal), -45 to -35 dB (rock)
- Ratio: 10:1 to 20:1
- Attack: 1-5 ms
- Release: 20-50 ms
- Gate Type: Classic/Intelligent
- Sidechain: Input 1

**DRIVE Block**:
- Type: {', '.join(['T808 MOD', 'Full OD', 'BB Pre', 'Rat Dist'])}
- Drive: 2-6 (boost), 5-8 (overdrive), 7-10 (distortion)
- Level: 4-7 (unity gain ~5)
- Tone: 4-7
- Mix: 100% (standard), 50-70% (parallel)

**AMP 1/AMP 2 Block**:
- Model: {', '.join(amp_suggestions[:3])}
- Input Drive: 3-8
- Bass/Mid/Treble: specific values 0-10
- Presence: 4-7
- Depth: 3-6
- Master Volume: 5-8
- Sag: 3-5
- Input Trim: 0.8-1.0

**CAB 1/CAB 2 Block**:
- IR: e.g. "4x12 Mesa V30 57+121" or Factory/User IR
- Low Cut: 80-120 Hz (12-24 dB/oct)
- High Cut: 7-10 kHz (12-24 dB/oct)
- Mic Distance: 0-6"
- Air: 0-5
- Air Freq: 2-4 kHz

**DELAY Block**:
- Type: Digital Mono/Stereo, Analog Mono, Tape, Vintage Digital
- Time: ms or sync (1/4, 1/8, 1/8D, 1/16)
- Feedback: 15-40%
- Mix: 10-30%
- Low Cut: 80-200 Hz
- High Cut: 5-8 kHz
- Ducking: 0-10 dB

**REVERB Block**:
- Type: Hall/Room/Plate/Spring/Chamber
- Decay: 1.5-4s
- Pre-delay: 0-50ms
- Mix: 5-25%
- Low Cut: 80-200 Hz
- High Cut: 4-8 kHz
- Early/Late Mix: 50/50
- Size: 10-50

3. SCENES (8) with CHANNEL SWITCHING:
Scene 1: Clean
  - DRIVE: Bypass
  - AMP 1: Channel A (Clean model)
  - DELAY: Channel A (subtle)
  - REVERB: Channel A (room)

Scene 2: Crunch  
  - DRIVE: Channel A (mild OD)
  - AMP 1: Channel B (edge of breakup)
  - DELAY: Channel A
  - REVERB: Channel A

Scene 3: Rhythm
  - DRIVE: Channel B (tighter)
  - AMP 1: Channel C (high gain)
  - GATE/EXP: Active
  - DELAY: Bypass
  - REVERB: Channel B (smaller)

Scene 4: Lead
  - DRIVE: Channel C (boost)
  - AMP 1: Channel C
  - DELAY: Channel B (more feedback)
  - REVERB: Channel C (hall)

Scene 5-8: User defined variations

4. CPU OPTIMIZATION:
- Total usage < 85%
- Quality IR instead of multiple cab blocks
- Efficient routing

5. GAIN STAGING and UNITY GAIN:
- Each block should maintain unity gain (input = output)
- Drive Level: ~5 for unity gain
- Amp Master Volume: 5-7 (affects tone and gain)
- Compressor: Makeup gain compensates reduction
- EQ: Use Output Level to compensate boosts
- Mixer: Balance dry/wet signal without changing overall gain

6. RESPONSE FORMAT:
- Start with grid representation
- Continue with detailed settings for EACH block including gain staging
- Include scenes and their changes including channel switching
- Add tips for gain staging and live playing
- Include approximate CPU usage

REMEMBER: Values must be SPECIFIC and REALISTIC for Axe-Fx III!"""

        if self.parallel_routing_var.get():
            prompt += "\n\nUSE PARALLEL ROUTING for delay/reverb (wet/dry mix) using MIXER blocks."
            
        if self.spillover_var.get():
            prompt += "\n\nSET SPILLOVER: Bypass Mode = 'MUTE FX IN' for delay and reverb."
            
        # 4-cable method if selected
        if style == "4CM":
            prompt += "\n\n4-CABLE METHOD: IN 1 → pre effects → SEND → (ext amp) → RETURN → post effects → OUT 1"
            
        return prompt
        
    def process_response(self, response_text):
        """Processes response from AI with mandatory I/O block enforcement."""
        # Stop CPU monitoring
        self.stop_cpu_monitoring()

        # Parse grid
        grid_match = re.search(r'```grid\n(.*?)```', response_text, re.DOTALL)
        if grid_match:
            grid_text = grid_match.group(1).strip()
            self.parse_and_display_grid(grid_text)
        else:
            # If no grid found, create a basic one from the response
            self.create_preset_from_response_text(response_text)

        # CRITICAL: Enforce I/O blocks after parsing
        self.enforce_mandatory_io_blocks()

        # Display formatted output
        self.display_formatted_output(response_text)

        # CRITICAL: Validate preset for required I/O blocks
        self.validate_and_display_preset_status()

        # Display web search enhancement results if available
        self.display_web_search_results()

        # Validate gain staging
        all_blocks = []
        for row in self.grid_cells:
            for cell in row:
                if cell["block"] and cell["block"] not in ['.', '---', '|', 'X', None]:
                    all_blocks.append(cell["block"])

        gain_warnings = self.validate_gain_staging(all_blocks)
        if gain_warnings:
            self.output_text.insert("end", "\n\n=== GAIN STAGING TIPS ===\n", "heading2")
            for warning in gain_warnings:
                self.output_text.insert("end", warning + "\n", "parameter")
        
        # Update status
        self.update_status("Preset generated!", "green")
        self.generate_button.configure(state="normal", text="Generate preset")
        
        # Update preset info
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        preset_name = f"Generated_{self.style_combobox.get()}_{timestamp}"
        self.preset_name_label.configure(text=f"Preset: {preset_name}")

    def display_web_search_results(self):
        """Displays web search enhancement results in the output."""
        if not hasattr(self, 'last_web_enhancement') or not self.last_web_enhancement:
            return

        enhancement = self.last_web_enhancement

        if enhancement.get("search_performed"):
            self.output_text.insert("end", "\n\n=== WEB SEARCH ENHANCEMENT ===\n", "heading2")

            # Display tone references
            if enhancement.get("tone_references", {}).get("search_successful"):
                tone_ref = enhancement["tone_references"]
                self.output_text.insert("end", f"🎸 ARTIST REFERENCE: {tone_ref.get('artist', 'Unknown')}\n", "parameter")
                if tone_ref.get("song"):
                    self.output_text.insert("end", f"🎵 SONG: {tone_ref['song']}\n", "parameter")

                tone_info = tone_ref.get("tone_info", [])
                if tone_info:
                    self.output_text.insert("end", "📝 TONE CHARACTERISTICS:\n", "parameter")
                    for info in tone_info[:2]:  # Show top 2 results
                        snippet = info.get("snippet", "")[:150]
                        self.output_text.insert("end", f"  • {snippet}...\n", "value")

            # Display equipment information
            if enhancement.get("equipment_info", {}).get("search_successful"):
                equipment_info = enhancement["equipment_info"]
                self.output_text.insert("end", f"🎛️ EQUIPMENT: {equipment_info.get('equipment', 'Unknown')}\n", "parameter")

                results = equipment_info.get("search_results", [])
                if results:
                    for result in results[:2]:  # Show top 2 results
                        snippet = result.get("snippet", "")[:150]
                        self.output_text.insert("end", f"  • {snippet}...\n", "value")

            # Display genre techniques
            if enhancement.get("genre_info", {}).get("search_successful"):
                genre_info = enhancement["genre_info"]
                self.output_text.insert("end", f"🎼 GENRE TECHNIQUES: {genre_info.get('genre', 'Unknown')}\n", "parameter")

                techniques = genre_info.get("techniques", [])
                if techniques:
                    for technique in techniques[:2]:  # Show top 2 results
                        snippet = technique.get("snippet", "")[:150]
                        self.output_text.insert("end", f"  • {snippet}...\n", "value")

            # Display firmware information
            firmware_info = enhancement.get("firmware_info", {})
            if firmware_info.get("success"):
                fw_details = firmware_info.get("firmware_info", {})
                if fw_details.get("version") != "Unknown":
                    self.output_text.insert("end", f"🔧 CURRENT FIRMWARE: {fw_details['version']}\n", "parameter")

            # Display sources
            sources = enhancement.get("sources", [])
            if sources:
                self.output_text.insert("end", "\n📚 SOURCES CONSULTED:\n", "parameter")
                for source in sources[:5]:  # Show top 5 sources
                    self.output_text.insert("end", f"  • {source}\n", "value")

            self.output_text.insert("end", "\n✅ Preset enhanced with real-time web information\n", "heading2")

    def enforce_mandatory_io_blocks(self):
        """Enforces that every preset has mandatory INPUT and OUTPUT blocks."""
        # Check current grid for I/O blocks
        has_input = False
        has_output = False
        all_blocks = []

        for row in self.grid_cells:
            for cell in row:
                if cell["block"] and cell["block"] not in ['.', '---', '|', 'X', None]:
                    all_blocks.append(cell["block"])
                    if any(inp in cell["block"] for inp in REQUIRED_INPUT_BLOCKS):
                        has_input = True
                    if any(out in cell["block"] for out in REQUIRED_OUTPUT_BLOCKS):
                        has_output = True

        # If missing I/O blocks, add them automatically
        if not has_input or not has_output:
            # Clear grid and rebuild with proper I/O
            missing_blocks = []
            if not has_input:
                missing_blocks.append("IN 1")
            if not has_output:
                missing_blocks.append("OUT 1")

            # Create complete block list with I/O
            complete_blocks = []
            if not has_input:
                complete_blocks.append("IN 1")
            complete_blocks.extend(all_blocks)
            if not has_output:
                complete_blocks.append("OUT 1")

            # Use Fractal Grid Manager to create optimal layout
            grid_layout, connections, warnings = FractalGridManager.create_optimal_grid_layout(complete_blocks)

            # Clear and apply the corrected layout
            self.clear_grid()
            for row_idx, row in enumerate(grid_layout):
                for col_idx, block in enumerate(row):
                    if block:
                        self.update_grid_cell(row_idx, col_idx, block)

            # Update visual connections
            self.update_signal_connections()

            # Notify user of the correction
            missing_text = ", ".join(missing_blocks)
            self.update_status(f"✅ Added mandatory I/O blocks: {missing_text}", "green")

            # Add note to output
            self.output_text.insert("end", f"\n\n🔧 AUTO-CORRECTION APPLIED:\n", "heading2")
            self.output_text.insert("end", f"Added missing mandatory blocks: {missing_text}\n", "parameter")
            self.output_text.insert("end", "Every Axe-Fx III preset MUST have INPUT and OUTPUT blocks to function!\n", "parameter")

    def create_preset_from_response_text(self, response_text):
        """Creates a preset from AI response text when no grid is found."""
        # Extract block names from the response text
        blocks = []

        # Look for common block patterns in the text
        block_patterns = [
            r'\b(IN \d+)\b',
            r'\b(GATE/EXP)\b',
            r'\b(COMPRESSOR)\b',
            r'\b(DRIVE)\b',
            r'\b(AMP \d+)\b',
            r'\b(CAB \d+)\b',
            r'\b(GEQ)\b',
            r'\b(PEQ)\b',
            r'\b(CHORUS)\b',
            r'\b(FLANGER)\b',
            r'\b(PHASER)\b',
            r'\b(TREMOLO)\b',
            r'\b(DELAY)\b',
            r'\b(MULTI DELAY)\b',
            r'\b(REVERB)\b',
            r'\b(SPRING)\b',
            r'\b(HALL)\b',
            r'\b(OUT \d+)\b'
        ]

        for pattern in block_patterns:
            matches = re.findall(pattern, response_text, re.IGNORECASE)
            blocks.extend(matches)

        # Remove duplicates while preserving order
        unique_blocks = []
        for block in blocks:
            if block not in unique_blocks:
                unique_blocks.append(block)

        # If we found blocks, create a preset
        if unique_blocks:
            # Use Fractal Grid Manager to create optimal layout
            grid_layout, connections, warnings = FractalGridManager.create_optimal_grid_layout(unique_blocks)

            # Apply the layout
            for row_idx, row in enumerate(grid_layout):
                for col_idx, block in enumerate(row):
                    if block:
                        self.update_grid_cell(row_idx, col_idx, block)

            self.update_status("✅ Preset created from AI description", "green")
        else:
            # Create a basic template if no blocks found
            basic_chain = PresetValidator.create_basic_signal_path()
            self.apply_template(basic_chain)
            self.update_status("✅ Created basic template - no specific blocks found in AI response", "yellow")

    def parse_and_display_grid(self, grid_text):
        """Parses and displays grid following Fractal Audio standards."""
        lines = grid_text.strip().split('\n')

        # Clear grid first
        self.clear_grid()

        # Parse blocks from grid text
        blocks = []
        for row_idx, line in enumerate(lines[:GRID_ROWS]):
            tokens = line.split()
            for col_idx, token in enumerate(tokens[:GRID_COLS]):
                if token and token not in ['.', '---', '|', 'X']:
                    blocks.append(token)

        # Use Fractal Grid Manager to create optimal layout
        if blocks:
            grid_layout, connections, warnings = FractalGridManager.create_optimal_grid_layout(blocks)

            # Apply the optimized layout
            for row_idx, row in enumerate(grid_layout):
                for col_idx, block in enumerate(row):
                    if block and row_idx < len(self.grid_cells) and col_idx < len(self.grid_cells[row_idx]):
                        self.update_grid_cell(row_idx, col_idx, block)

            # Show warnings if any
            if warnings:
                warning_text = "; ".join(warnings)
                self.update_status(f"Grid optimized with notes: {warning_text}", "yellow")
        else:
            # If no blocks found, parse as-is for visualization
            for row_idx, line in enumerate(lines[:GRID_ROWS]):
                tokens = line.split()
                for col_idx, token in enumerate(tokens[:GRID_COLS]):
                    if row_idx < len(self.grid_cells) and col_idx < len(self.grid_cells[row_idx]):
                        self.update_grid_cell(row_idx, col_idx, token)

        # Validate signal flow and update visual indicators
        self.validate_signal_flow_visual()

        # Calculate CPU usage
        self.calculate_cpu_usage()
        
    def update_grid_cell(self, row, col, token):
        """Updates cell in grid following Fractal Audio signal flow standards."""
        if row >= len(self.grid_cells) or col >= len(self.grid_cells[row]):
            return

        cell = self.grid_cells[row][col]

        # Clear previous state
        cell["block"] = None
        cell["signal_flow_position"] = None
        cell["is_valid_position"] = True

        # Handle empty cells and connections
        if token in ['.', '---', '|', 'X', None]:
            if token == '.':
                color = BLOCK_COLORS["empty"]
                text = ""
                flow_text = ""
            else:
                color = BLOCK_COLORS["SHUNT"]
                text = token if token else ""
                flow_text = ""
        else:
            # Get block information using Fractal Grid Manager
            color = BLOCK_COLORS.get(token, BLOCK_COLORS["default"])
            text = token

            # Get signal flow information
            flow_position = FractalGridManager.get_signal_flow_position(token)
            category = FractalGridManager.get_block_category(token)

            cell["block"] = token
            cell["signal_flow_position"] = flow_position

            # Create flow indicator text
            if flow_position < 999:
                flow_text = f"{category}"
            else:
                flow_text = "SPECIAL"

            # Validate position in signal flow
            self.validate_block_position(row, col, token, flow_position)

        # Update visual elements
        self.grid_canvas.itemconfig(cell["rect"], fill=color)
        self.grid_canvas.itemconfig(cell["text"], text=text)
        self.grid_canvas.itemconfig(cell["flow_indicator"], text=flow_text)

        # Update connections
        self.update_signal_connections()

    def validate_block_position(self, row, col, block, flow_position):
        """Validates if a block is in the correct position according to Fractal Audio standards."""
        cell = self.grid_cells[row][col]

        # Check if this block is in correct order relative to other blocks
        is_valid = True

        # Check blocks to the left (should have lower flow positions)
        for check_col in range(col):
            check_cell = self.grid_cells[row][check_col]
            if (check_cell["block"] and
                check_cell["signal_flow_position"] is not None and
                check_cell["signal_flow_position"] > flow_position and
                check_cell["signal_flow_position"] < 999 and
                flow_position < 999):
                is_valid = False
                break

        # Check blocks to the right (should have higher flow positions)
        for check_col in range(col + 1, GRID_COLS):
            check_cell = self.grid_cells[row][check_col]
            if (check_cell["block"] and
                check_cell["signal_flow_position"] is not None and
                check_cell["signal_flow_position"] < flow_position and
                check_cell["signal_flow_position"] < 999 and
                flow_position < 999):
                is_valid = False
                break

        cell["is_valid_position"] = is_valid

        # Update visual indication of validity
        if not is_valid and block:
            # Highlight invalid position with warning color
            self.grid_canvas.itemconfig(cell["rect"], outline="#FF6B6B", width=3)
            self.grid_canvas.itemconfig(cell["flow_indicator"], fill="#FF6B6B")
        else:
            # Normal styling
            self.grid_canvas.itemconfig(cell["rect"], outline="#555555", width=2)
            self.grid_canvas.itemconfig(cell["flow_indicator"], fill="#666666")

    def update_signal_connections(self):
        """Updates visual signal path connections following Fractal Audio logic."""
        # Hide all connections first
        for row in self.grid_cells:
            for cell in row:
                if cell["connection"]:
                    self.grid_canvas.itemconfig(cell["connection"], state="hidden")

        # Show connections between adjacent blocks that form a logical signal path
        for row_idx, row in enumerate(self.grid_cells):
            for col_idx, cell in enumerate(row):
                if (cell["block"] and
                    cell["connection"] and
                    col_idx < GRID_COLS - 1):

                    next_cell = self.grid_cells[row_idx][col_idx + 1]

                    # Show connection if next cell has a block
                    if next_cell["block"]:
                        # Determine connection color based on signal flow validity
                        if (cell["is_valid_position"] and
                            next_cell["is_valid_position"] and
                            self.is_valid_signal_connection(cell, next_cell)):
                            # Valid signal flow - green connection
                            connection_color = "#4CAF50"
                            connection_width = 3
                        else:
                            # Invalid signal flow - red connection
                            connection_color = "#FF6B6B"
                            connection_width = 2

                        self.grid_canvas.itemconfig(
                            cell["connection"],
                            state="normal",
                            fill=connection_color,
                            width=connection_width
                        )

    def is_valid_signal_connection(self, from_cell, to_cell):
        """Checks if a connection between two cells follows Fractal Audio signal flow."""
        if not from_cell["block"] or not to_cell["block"]:
            return False

        from_pos = from_cell["signal_flow_position"]
        to_pos = to_cell["signal_flow_position"]

        # Valid if signal flows from lower to higher position (or special blocks)
        if from_pos is None or to_pos is None:
            return True  # Allow special blocks

        if from_pos >= 999 or to_pos >= 999:
            return True  # Allow special blocks

        return from_pos <= to_pos

    def validate_signal_flow_visual(self):
        """Validates and highlights signal flow issues in the visual grid."""
        # Perform validation using Fractal Grid Manager
        is_valid, errors, warnings, suggestions = FractalGridManager.validate_signal_flow(self.grid_cells)

        # Update all cell positions
        for row_idx, row in enumerate(self.grid_cells):
            for col_idx, cell in enumerate(row):
                if cell["block"]:
                    flow_position = FractalGridManager.get_signal_flow_position(cell["block"])
                    self.validate_block_position(row_idx, col_idx, cell["block"], flow_position)

        # Update connections
        self.update_signal_connections()

        # Show validation results
        if is_valid:
            self.update_status("✅ Signal flow follows Fractal Audio standards", "green")
        else:
            error_summary = f"⚠️ Signal flow issues: {len(errors)} errors, {len(warnings)} warnings"
            self.update_status(error_summary, "orange")

        # Display detailed validation in output
        self.validate_and_display_preset_status()
        
    def display_formatted_output(self, text):
        """Displays formatted output."""
        self.output_text.delete("1.0", "end")
        
        # Removing grid section
        text = re.sub(r'```grid.*?```', '', text, flags=re.DOTALL)
        
        # Formatting sections
        sections = text.split('\n\n')
        
        for section in sections:
            if section.strip():
                # Detecting section type
                if section.startswith('**') and 'Block' in section:
                    # Block heading
                    lines = section.split('\n')
                    self.output_text.insert("end", lines[0].strip('*') + '\n', "heading2")
                    
                    for line in lines[1:]:
                        if ':' in line:
                            parts = line.split(':', 1)
                            self.output_text.insert("end", parts[0] + ": ", "parameter")
                            self.output_text.insert("end", parts[1].strip() + '\n', "value")
                        else:
                            self.output_text.insert("end", line + '\n')
                            
                elif section.startswith('#'):
                    # Main heading
                    self.output_text.insert("end", section.strip('#').strip() + '\n', "heading1")
                    
                else:
                    # Normal text
                    self.output_text.insert("end", section + '\n\n')
                    
    def calculate_cpu_usage(self) -> None:
        """Optimized CPU usage calculation using centralized costs."""
        total_cpu: float = 0.0
        block_count: Dict[str, int] = {}

        # Optimized single-pass counting
        for row in self.grid_cells:
            for cell in row:
                block = cell.get("block")
                if block and block not in ['.', '---', '|', 'X', None]:
                    total_cpu += CPU_COSTS.get(block, 1.0)
                    block_count[block] = block_count.get(block, 0) + 1
                    
        # Update CPU display
        cpu_percentage = min(total_cpu, 90)
        self.cpu_progress.set(cpu_percentage / 100)
        self.cpu_label.configure(text=f"CPU: {cpu_percentage:.1f}% / 90% limit")
        
        # Warning if over limit
        if cpu_percentage >= 85:
            self.cpu_warning.configure(
                text="⚠️ High CPU usage - consider optimization",
                text_color="orange"
            )
        elif cpu_percentage >= 90:
            self.cpu_warning.configure(
                text="❌ CPU limit exceeded!",
                text_color="red"
            )
        else:
            self.cpu_warning.configure(text="")
            
        # Update info with new block names
        blocks_info = f"Blocks: {sum(block_count.values())} | " + \
                     f"Amp 1: {block_count.get('AMP 1', 0)} | " + \
                     f"Amp 2: {block_count.get('AMP 2', 0)} | " + \
                     f"Cabs: {block_count.get('CAB 1', 0) + block_count.get('CAB 2', 0)}"
        self.info_label.configure(text=blocks_info)
        
    def start_cpu_monitoring(self):
        """Starts CPU monitor animation."""
        self.cpu_meter_active = True
        self._animate_cpu()
        
    def stop_cpu_monitoring(self):
        """Stops CPU monitor animation."""
        self.cpu_meter_active = False
        
    def _animate_cpu(self):
        """Animates CPU meter during generation."""
        if self.cpu_meter_active:
            current = self.cpu_progress.get()
            target = 0.3 + (0.4 * abs(hash(str(datetime.now())) % 100) / 100)
            new_value = current + (target - current) * 0.1
            self.cpu_progress.set(new_value)
            self.cpu_label.configure(text=f"CPU: Calculating...")
            self.after(50, self._animate_cpu)
            
    # === Grid Manipulation Functions ===
    
    def clear_grid(self):
        """Clears the grid following Fractal Audio standards."""
        for row in self.grid_cells:
            for cell in row:
                # Clear visual elements
                self.grid_canvas.itemconfig(cell["rect"], fill=BLOCK_COLORS["empty"], outline="#555555", width=2)
                self.grid_canvas.itemconfig(cell["text"], text="")
                self.grid_canvas.itemconfig(cell["flow_indicator"], text="")

                # Hide connections
                if cell["connection"]:
                    self.grid_canvas.itemconfig(cell["connection"], state="hidden")

                # Clear data
                cell["block"] = None
                cell["signal_flow_position"] = None
                cell["is_valid_position"] = True

        # Reset CPU monitoring
        self.cpu_progress.set(0)
        self.cpu_label.configure(text="CPU: 0% / 90% limit")
        self.cpu_warning.configure(text="")

        # Update status
        self.update_status("Grid cleared - ready for Fractal Audio preset construction", "white")
        
    def auto_route(self):
        """Automatically connects blocks in grid with intelligent routing."""
        # Find all blocks and create connections between them
        blocks = []

        # Find all blocks
        for row_idx, row in enumerate(self.grid_cells):
            for col_idx, cell in enumerate(row):
                if cell["block"] and cell["block"] not in ['.', '---', '|', 'X', None]:
                    blocks.append((row_idx, col_idx, cell["block"]))

        if len(blocks) == 0:
            response = messagebox.askyesno(
                "No Blocks Found",
                f"No blocks found in the grid.\n\n{MANDATORY_IO_MESSAGE}\n"
                "Would you like to create a basic functional template?"
            )
            if response:
                basic_path = PresetValidator.create_basic_signal_path()
                self.apply_template(basic_path)
                self.update_status("✅ Basic I/O template created", "green")
            return

        if len(blocks) < 2:
            self.show_error("Need at least 2 blocks for routing")
            return

        # Find input and output blocks
        input_blocks = [b for b in blocks if "IN" in b[2]]
        output_blocks = [b for b in blocks if "OUT" in b[2]]

        # CRITICAL: Check for mandatory I/O blocks
        missing_inputs, missing_outputs = PresetValidator.get_missing_io_blocks(self.grid_cells)
        missing_blocks = missing_inputs + missing_outputs

        if missing_blocks:
            response = messagebox.askyesno(
                "🚨 CRITICAL: Missing Essential I/O Blocks",
                f"{MANDATORY_IO_MESSAGE}\n"
                f"Missing blocks: {', '.join(missing_blocks)}\n\n"
                "Add these essential blocks automatically?\n"
                "(Required for preset to function on hardware)"
            )
            if response:
                self.auto_add_io_blocks(blocks, missing_blocks)
                # Refresh blocks list
                blocks = []
                for row_idx, row in enumerate(self.grid_cells):
                    for col_idx, cell in enumerate(row):
                        if cell["block"] and cell["block"] not in ['.', '---', '|', 'X', None]:
                            blocks.append((row_idx, col_idx, cell["block"]))
                input_blocks = [b for b in blocks if "IN" in b[2]]
                output_blocks = [b for b in blocks if "OUT" in b[2]]
                self.update_status("✅ Essential I/O blocks added", "green")
            else:
                self.update_status("⚠️ Preset will not function without I/O blocks", "red")
                return
            
        # Sort blocks by position (left to right, top to bottom)
        blocks.sort(key=lambda x: (x[0], x[1]))
        
        # Detection of parallel routing
        if self.parallel_routing_var.get():
            self.create_parallel_routing(blocks)
        else:
            self.create_serial_routing(blocks)
            
        self.calculate_cpu_usage()
        self.update_status("Auto-routing completed", "green")

    def auto_add_io_blocks(self, existing_blocks, missing_blocks):
        """Automatically adds missing input/output blocks to the grid."""
        # Find available positions
        used_positions = {(row, col) for row, col, _ in existing_blocks}

        for block_type in missing_blocks:
            if block_type == "IN 1":
                # Place input at the beginning (leftmost available position)
                for col in range(GRID_COLS):
                    for row in range(GRID_ROWS):
                        if (row, col) not in used_positions:
                            self.update_grid_cell(row, col, "IN 1")
                            used_positions.add((row, col))
                            break
                    else:
                        continue
                    break

            elif block_type == "OUT 1":
                # Place output at the end (rightmost available position)
                for col in range(GRID_COLS - 1, -1, -1):
                    for row in range(GRID_ROWS):
                        if (row, col) not in used_positions:
                            self.update_grid_cell(row, col, "OUT 1")
                            used_positions.add((row, col))
                            break
                    else:
                        continue
                    break
        
    def create_serial_routing(self, blocks):
        """Creates serial connection of blocks."""
        for i in range(len(blocks) - 1):
            curr_row, curr_col, curr_block = blocks[i]
            next_row, next_col, next_block = blocks[i + 1]
            
            # Horizontal connection on same row
            if curr_row == next_row:
                for col in range(curr_col + 1, next_col):
                    if self.grid_cells[curr_row][col]["block"] is None:
                        self.update_grid_cell(curr_row, col, "---")
                        
            # Vertical or diagonal connection
            else:
                # First horizontally to target column
                for col in range(curr_col + 1, next_col):
                    if curr_row < len(self.grid_cells) and col < len(self.grid_cells[curr_row]):
                        if self.grid_cells[curr_row][col]["block"] is None:
                            self.update_grid_cell(curr_row, col, "---")
                
                # Then vertically down
                for row in range(curr_row + 1, next_row):
                    if row < len(self.grid_cells) and next_col < len(self.grid_cells[row]):
                        if self.grid_cells[row][next_col]["block"] is None:
                            self.update_grid_cell(row, next_col, "|")
                            
    def create_parallel_routing(self, blocks):
        """Creates parallel routing for wet/dry mix."""
        # Find delay and reverb blocks
        delay_blocks = [b for b in blocks if "DELAY" in b[2]]
        reverb_blocks = [b for b in blocks if "REVERB" in b[2] or "HALL" in b[2] or "ROOM" in b[2]]
        
        if delay_blocks or reverb_blocks:
            # Implementation of parallel routing
            # TODO: Implement split/merge blocks
            self.create_serial_routing(blocks)  # Use serial for now
            self.update_status("Parallel routing prepared (requires MIXER blocks)", "yellow")
        
    # === Builder Functions ===
    
    def select_block(self, block_type):
        """Selects block for placement."""
        self.selected_block = block_type
        self.selected_block_label.configure(text=f"Selected: {block_type}")
        
    # Removed redundant get_cpu_costs method - now using centralized CPU_COSTS constant
    
    # New Drag and Drop Methods
    def on_palette_click(self, event, block_type):
        """Handles click on palette button to start drag."""
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root
        self.potential_drag_block = block_type
        self.drag_threshold = 5  # Minimum pixels to start drag
        
    def place_block_in_cell(self, row, col, block_type):
        """Places a block in the specified grid cell."""
        cell = self.builder_grid_cells[row][col]
        
        # Don't place if cell is already occupied
        if cell["block"]:
            self.update_status(f"Cell ({row+1}, {col+1}) is already occupied", "orange")
            return False
            
        # Place the block
        color = BLOCK_COLORS.get(block_type, BLOCK_COLORS["default"])
        
        # Update cell rectangle color
        self.builder_canvas.itemconfig(cell["rect"], fill=color, outline="#333333")
        
        # Update cell text
        self.builder_canvas.itemconfig(cell["text"], text=block_type, fill="white")
        
        # Update cell data
        cell["block"] = block_type
        
        # Update preset information
        self.update_preset_info()
        
        return True
        
    # Grid-to-Grid Drag Methods
    def move_block(self, from_row, from_col, to_row, to_col):
        """Moves a block from one cell to another."""
        source_cell = self.builder_grid_cells[from_row][from_col]
        target_cell = self.builder_grid_cells[to_row][to_col]
        
        block_type = source_cell["block"]
        color = BLOCK_COLORS.get(block_type, BLOCK_COLORS["default"])
        
        # Clear source cell
        source_cell["block"] = None
        self.builder_canvas.itemconfig(source_cell["rect"], fill=BLOCK_COLORS["empty"], outline="#666666")
        self.builder_canvas.itemconfig(source_cell["text"], text="", fill="white")
        
        # Fill target cell
        target_cell["block"] = block_type
        self.builder_canvas.itemconfig(target_cell["rect"], fill=color, outline="#333333")
        self.builder_canvas.itemconfig(target_cell["text"], text=block_type, fill="white")
        
        # Update preset info
        self.update_preset_info()
        
    def remove_block(self, row, col):
        """Removes a block from the grid."""
        cell = self.builder_grid_cells[row][col]
        
        # Clear the cell
        cell["block"] = None
        self.builder_canvas.itemconfig(cell["rect"], fill=BLOCK_COLORS["empty"], outline="#666666")
        self.builder_canvas.itemconfig(cell["text"], text="", fill="white")
        
        # Update preset info
        self.update_preset_info()
        
    def toggle_delete_mode(self):
        """Toggles to deletion mode."""
        self.delete_mode = not hasattr(self, 'delete_mode') or not self.delete_mode
        self.connect_mode = False
        
        if self.delete_mode:
            self.selected_block = None
            self.selected_block_label.configure(text="Mode: DELETE")
            self.update_status("Delete mode active - click on block to delete", "red")
        else:
            self.selected_block_label.configure(text="Selected: None")
            self.update_status("Delete mode deactivated", "white")
            
    # === Template Functions ===
    
    def apply_template(self, chain):
        """Applies template to grid following official Fractal Audio procedures."""
        self.clear_grid()

        # Use Fractal Grid Manager for optimal layout
        grid_layout, connections, warnings = FractalGridManager.create_optimal_grid_layout(chain)

        # Apply the optimized layout to the grid
        for row_idx, row in enumerate(grid_layout):
            for col_idx, block in enumerate(row):
                if block:
                    self.update_grid_cell(row_idx, col_idx, block)

        # Update visual signal flow connections
        self.update_signal_connections()

        # Validate signal flow visually
        self.validate_signal_flow_visual()

        # Show any warnings from the layout process
        if warnings:
            warning_text = "; ".join(warnings)
            self.update_status(f"Template applied with notes: {warning_text}", "yellow")
        else:
            self.update_status("✅ Template applied following Fractal Audio best practices", "green")

        # Validate the applied template
        self.validate_and_display_preset_status()
        
        # Automatically connect blocks
        for i in range(GRID_COLS - 1):
            curr_cell = self.grid_cells[0][i]
            next_cell = self.grid_cells[0][i + 1]
            if curr_cell["block"] and next_cell["block"] and curr_cell["block"] != "---":
                # No need to connect, they are adjacent
                pass
            elif curr_cell["block"] and not next_cell["block"]:
                # Add connection
                self.update_grid_cell(0, i + 1, "---")
                
        # Calculate CPU
        self.calculate_cpu_usage()
        
        # Display in output text
        self.output_text.delete("1.0", "end")
        self.output_text.insert("end", f"Template: {' → '.join(['IN 1'] + chain + ['OUT 1'])}\n", "heading1")
        self.output_text.insert("end", "\nTemplate successfully applied. You can edit parameters of individual blocks.\n")
        
        self.update_status(f"Template applied: IN 1 → {' → '.join(chain)} → OUT 1", "green")
        
    def load_artist_preset(self, artist):
        """Loads preset according to artist."""
        preset_data = ARTIST_PRESETS[artist]
        
        # Display info in output
        self.output_text.delete("1.0", "end")
        self.output_text.insert("end", f"{artist} Preset\n", "heading1")
        self.output_text.insert("end", "\n")
        
        # Amps
        self.output_text.insert("end", "Amplifiers:\n", "heading2")
        for amp in preset_data["amps"]:
            self.output_text.insert("end", f"• {amp}\n", "value")
        self.output_text.insert("end", "\n")
        
        # Other settings
        for key, value in preset_data.items():
            if key != "amps":
                self.output_text.insert("end", f"{key.capitalize()}: ", "parameter")
                self.output_text.insert("end", f"{value}\n", "value")
                
        self.update_status(f"Loaded preset: {artist}", "green")
        
    # === Export/Import Functions ===
    
    def export_preset(self):
        """Exports preset to various formats with validation."""
        # CRITICAL: Validate preset before export
        is_valid, errors, warnings, suggestions = PresetValidator.validate_preset(self.grid_cells)

        if not is_valid:
            response = messagebox.askyesno(
                "🚨 INVALID PRESET - Cannot Export",
                f"{MANDATORY_IO_MESSAGE}\n"
                "CRITICAL ERRORS:\n" + "\n".join(errors) + "\n\n"
                "This preset will NOT work on Axe-Fx III hardware!\n\n"
                "Export anyway? (NOT RECOMMENDED)"
            )
            if not response:
                self.update_status("Export cancelled - fix validation errors first", "red")
                return

        # Dialog for format selection
        export_dialog = ctk.CTkToplevel(self)
        export_dialog.title("Export Preset")
        export_dialog.geometry("400x300")
        
        ctk.CTkLabel(
            export_dialog,
            text="Select export format:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=20)
        
        # Export options
        ctk.CTkButton(
            export_dialog,
            text="Text file (.txt)",
            command=lambda: self.export_as_text(export_dialog),
            width=200
        ).pack(pady=5)
        
        ctk.CTkButton(
            export_dialog,
            text="JSON preset (.json)",
            command=lambda: self.export_as_json(export_dialog),
            width=200
        ).pack(pady=5)
        
        ctk.CTkButton(
            export_dialog,
            text="Sysex for Axe-Fx (.syx)",
            command=lambda: self.export_as_sysex(export_dialog),
            width=200
        ).pack(pady=5)
        
        ctk.CTkButton(
            export_dialog,
            text="Markdown (.md)",
            command=lambda: self.export_as_markdown(export_dialog),
            width=200
        ).pack(pady=5)
        
        ctk.CTkButton(
            export_dialog,
            text="Cancel",
            command=export_dialog.destroy,
            width=200,
            fg_color="gray"
        ).pack(pady=20)
        
    def export_as_text(self, dialog):
        """Export as text file."""
        dialog.destroy()
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("=== Axe-Fx III Preset ===\n")
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"Style: {self.style_combobox.get()}\n\n")
                    
                    # Grid
                    f.write("=== Signal Path Grid ===\n")
                    for row in self.grid_cells:
                        for cell in row:
                            block = cell.get("block", ".")
                            if block is None:
                                block = "."
                            f.write(f"{block:^5}")
                        f.write("\n")
                    f.write("\n")
                    
                    # Detailed settings
                    f.write("=== Detailed Settings ===\n")
                    f.write(self.output_text.get("1.0", "end"))
                    
                self.update_status(f"Preset exported: {filename}", "green")
                
            except Exception as e:
                self.show_error(f"Export error: {str(e)}")
                
    def export_as_json(self, dialog):
        """Export as JSON."""
        dialog.destroy()
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                preset_data = {
                    "name": self.preset_name_label.cget("text").replace("Preset: ", ""),
                    "date": datetime.now().isoformat(),
                    "style": self.style_combobox.get(),
                    "grid": [],
                    "blocks": {},
                    "scenes": {},
                    "cpu": self.cpu_progress.get() * 100,
                    "description": self.query_textbox.get("1.0", "end").strip()
                }
                
                # Grid data
                for row_idx, row in enumerate(self.grid_cells):
                    row_data = []
                    for col_idx, cell in enumerate(row):
                        block = cell.get("block", ".")
                        if block is None:
                            block = "."
                        row_data.append(block)
                        
                        # Store block details
                        if block not in [".", "---", "|", "X", None]:
                            block_id = f"{block}_{row_idx}_{col_idx}"
                            preset_data["blocks"][block_id] = {
                                "type": block,
                                "row": row_idx,
                                "col": col_idx,
                                "parameters": {}  # TODO: Add actual parameters
                            }
                            
                    preset_data["grid"].append(row_data)
                    
                with open(filename, 'w') as f:
                    json.dump(preset_data, f, indent=2)
                    
                self.update_status(f"JSON preset exported: {filename}", "green")
                
            except Exception as e:
                self.show_error(f"Export error: {str(e)}")
                
    def export_as_sysex(self, dialog):
        """Export jako Sysex (simulace)."""
        dialog.destroy()
        messagebox.showinfo(
            "Sysex Export",
            "Sysex export requires Fractal Bot software.\n\n"
            "1. Save preset as .json\n"
            "2. Use Fractal Bot for conversion\n"
            "3. Upload to Axe-Fx III via USB"
        )
        
    def export_as_markdown(self, dialog):
        """Export as Markdown."""
        dialog.destroy()
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".md",
            filetypes=[("Markdown files", "*.md"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"# Axe-Fx III Preset\n\n")
                    f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  \n")
                    f.write(f"**Style:** {self.style_combobox.get()}  \n")
                    f.write(f"**CPU Usage:** {self.cpu_progress.get() * 100:.1f}%  \n\n")
                    
                    f.write("## Signal Path\n\n")
                    f.write("```\n")
                    for row in self.grid_cells:
                        for cell in row:
                            block = cell.get("block", ".")
                            if block is None:
                                block = "."
                            f.write(f"{block:^5}")
                        f.write("\n")
                    f.write("```\n\n")
                    
                    f.write("## Detailed Settings\n\n")
                    f.write(self.output_text.get("1.0", "end"))
                    
                self.update_status(f"Markdown exported: {filename}", "green")
                
            except Exception as e:
                self.show_error(f"Export error: {str(e)}")
                
    def copy_to_clipboard(self):
        """Copies output to clipboard."""
        content = self.output_text.get("1.0", "end")
        self.clipboard_clear()
        self.clipboard_append(content)
        self.update_status("Copied to clipboard", "green")
        
    def switch_scene(self, scene_num):
        """Switches to different scene."""
        # TODO: Implement saving and loading scenes
        self.update_status(f"Scene {scene_num} active", "blue")
        
    def search_online_presets(self):
        """Opens dialog for online search."""
        search_dialog = ctk.CTkToplevel(self)
        search_dialog.title("Online Preset Search")
        search_dialog.geometry("500x400")
        
        # Search input
        search_frame = ctk.CTkFrame(search_dialog)
        search_frame.pack(fill="x", padx=20, pady=20)
        
        ctk.CTkLabel(
            search_frame,
            text="Search preset/sound:",
            font=ctk.CTkFont(size=14)
        ).pack(anchor="w", pady=(0, 5))
        
        search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="e.g. 'Metallica rhythm', 'John Petrucci lead'..."
        )
        search_entry.pack(fill="x", pady=(0, 10))
        
        # Search sources
        sources_frame = ctk.CTkFrame(search_dialog)
        sources_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        ctk.CTkLabel(
            sources_frame,
            text="Sources:",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(anchor="w", pady=10)
        
        # Search buttons for different sources
        sources = [
            ("Axe-Change (official)", "https://axechange.fractalaudio.com/search-results.php?search="),
            ("Fractal Forum", "https://forum.fractalaudio.com/search/?q="),
            ("YouTube tutorials", "https://www.youtube.com/results?search_query=axe+fx+iii+"),
            ("Reddit r/fractalaudio", "https://www.reddit.com/r/fractalaudio/search/?q="),
            ("Google Search", "https://www.google.com/search?q=axe+fx+iii+preset+")
        ]
        
        for source_name, base_url in sources:
            btn_frame = ctk.CTkFrame(sources_frame)
            btn_frame.pack(fill="x", pady=5)
            
            ctk.CTkButton(
                btn_frame,
                text=f"Search on {source_name}",
                command=lambda url=base_url: self.open_search(url, search_entry.get()),
                width=300
            ).pack(padx=10, pady=5)
            
        # Help text
        help_text = ctk.CTkLabel(
            search_dialog,
            text="Tip: After finding a preset you can manually\nenter parameters into the application for generation.",
            font=ctk.CTkFont(size=11),
            text_color="gray"
        )
        help_text.pack(pady=10)
        
        # Close button
        ctk.CTkButton(
            search_dialog,
            text="Close",
            command=search_dialog.destroy,
            width=150,
            fg_color="gray"
        ).pack(pady=10)
        
    def open_search(self, base_url, query):
        """Opens search in browser."""
        if query:
            search_url = base_url + query.replace(" ", "+")
            webbrowser.open(search_url)
            self.update_status(f"Opening search: {query}", "blue")
        else:
            self.show_error("Enter search term")
            
    def on_style_change(self, choice):
        """Reacts to style change."""
        # Update recommended blocks
        recommended_chain = EFFECT_CHAINS.get(choice, [])
        
        # Show recommendations in status bar
        chain_text = "IN → " + " → ".join(recommended_chain) + " → OUT"
        self.info_label.configure(text=f"Recommended path: {chain_text}")
        
        # Automatically apply basic template if grid is empty
        is_empty = all(
            cell["block"] in [None, ".", "---", "|"] 
            for row in self.grid_cells 
            for cell in row
        )
        
        if is_empty:
            response = messagebox.askyesno(
                "Apply template?",
                f"Do you want to apply basic template for {choice}?"
            )
            if response:
                self.apply_template(recommended_chain)
                
    # === Utility Functions ===
    
    def update_status(self, message, color="white"):
        """Updates status bar."""
        self.status_label.configure(text=message, text_color=color)
        
    def show_error(self, message):
        """Displays error message."""
        try:
            messagebox.showerror("Error", message)
            self.update_status("Error", "red")
        except Exception as e:
            # Fallback if messagebox fails
            print(f"Error: {message}")
            print(f"Messagebox error: {e}")
            self.update_status("Error", "red")


if __name__ == "__main__":
    app = AxeFxPresetGeneratorPro()
    app.mainloop()